import React, { useState } from 'react'
import {
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Popconfirm,
  Typography,
  Card,
  Row,
  Col,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SearchOutlined,
  ReloadOutlined,
} from '@ant-design/icons'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { requirementApi, userApi } from '../services/api'
import type {
  Requirement,
  RequirementCreateRequest,
  RequirementUpdateRequest,
  RequirementStatus,
  Priority,
  User,
} from '../types'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { TextArea } = Input
const { Option } = Select

// 需求状态配置
const REQUIREMENT_STATUS_CONFIG = {
  DRAFT: { label: '草稿', color: 'default' },
  REVIEW: { label: '评审中', color: 'processing' },
  APPROVED: { label: '已批准', color: 'success' },
  IN_PROGRESS: { label: '进行中', color: 'warning' },
  TESTING: { label: '测试中', color: 'cyan' },
  DELIVERED: { label: '已交付', color: 'green' },
  REJECTED: { label: '已拒绝', color: 'error' },
}

// 优先级配置
const PRIORITY_CONFIG = {
  HIGH: { label: '高', color: 'red' },
  MEDIUM: { label: '中', color: 'orange' },
  LOW: { label: '低', color: 'green' },
}

const RequirementsPage: React.FC = () => {
  const [searchForm] = Form.useForm()
  const [createForm] = Form.useForm()
  const [editForm] = Form.useForm()
  const queryClient = useQueryClient()

  // 状态管理
  const [isCreateModalVisible, setIsCreateModalVisible] = useState(false)
  const [isEditModalVisible, setIsEditModalVisible] = useState(false)
  const [editingRequirement, setEditingRequirement] = useState<Requirement | null>(null)
  const [searchParams, setSearchParams] = useState({
    page: 1,
    pageSize: 20,
    status: undefined as RequirementStatus | undefined,
    assigneeId: undefined as number | undefined,
    creatorId: undefined as number | undefined,
  })

  // 获取需求列表
  const { data: requirementData, isLoading } = useQuery({
    queryKey: ['requirements', searchParams],
    queryFn: () => requirementApi.getRequirementList(searchParams),
  })

  // 获取用户列表（用于分配负责人）
  const { data: users } = useQuery({
    queryKey: ['users'],
    queryFn: userApi.getAllUsers,
  })

  // 创建需求
  const createMutation = useMutation({
    mutationFn: requirementApi.createRequirement,
    onSuccess: () => {
      message.success('需求创建成功')
      setIsCreateModalVisible(false)
      createForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '创建失败')
    },
  })

  // 更新需求
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: RequirementUpdateRequest }) =>
      requirementApi.updateRequirement(id, data),
    onSuccess: () => {
      message.success('需求更新成功')
      setIsEditModalVisible(false)
      setEditingRequirement(null)
      editForm.resetFields()
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '更新失败')
    },
  })

  // 删除需求
  const deleteMutation = useMutation({
    mutationFn: requirementApi.deleteRequirement,
    onSuccess: () => {
      message.success('需求删除成功')
      queryClient.invalidateQueries({ queryKey: ['requirements'] })
    },
    onError: (error: any) => {
      message.error(error.response?.data?.message || '删除失败')
    },
  })

  // 处理搜索
  const handleSearch = (values: any) => {
    setSearchParams({
      ...searchParams,
      page: 1,
      ...values,
    })
  }

  // 处理重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields()
    setSearchParams({
      page: 1,
      pageSize: 20,
      status: undefined,
      assigneeId: undefined,
      creatorId: undefined,
    })
  }

  // 处理创建需求
  const handleCreateRequirement = async (values: any) => {
    const data: RequirementCreateRequest = {
      ...values,
      expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
    }
    createMutation.mutate(data)
  }

  // 处理编辑需求
  const handleEditRequirement = (requirement: Requirement) => {
    setEditingRequirement(requirement)
    editForm.setFieldsValue({
      ...requirement,
      expectedDeliveryDate: requirement.expectedDeliveryDate
        ? dayjs(requirement.expectedDeliveryDate)
        : undefined,
      actualDeliveryDate: requirement.actualDeliveryDate
        ? dayjs(requirement.actualDeliveryDate)
        : undefined,
    })
    setIsEditModalVisible(true)
  }

  // 处理更新需求
  const handleUpdateRequirement = async (values: any) => {
    if (!editingRequirement) return

    const data: RequirementUpdateRequest = {
      ...values,
      expectedDeliveryDate: values.expectedDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
      actualDeliveryDate: values.actualDeliveryDate?.format('YYYY-MM-DD HH:mm:ss'),
    }
    updateMutation.mutate({ id: editingRequirement.id, data })
  }

  // 处理删除需求
  const handleDeleteRequirement = (id: number) => {
    deleteMutation.mutate(id)
  }

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '需求标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true,
    },
    {
      title: '业务描述',
      dataIndex: 'businessDescription',
      key: 'businessDescription',
      width: 250,
      ellipsis: true,
      render: (text: string) => (
        <Tooltip title={text}>
          <Text ellipsis>{text}</Text>
        </Tooltip>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: RequirementStatus) => {
        const config = REQUIREMENT_STATUS_CONFIG[status]
        return <Tag color={config.color}>{config.label}</Tag>
      },
    },
    {
      title: '重要程度',
      dataIndex: 'priorityImportance',
      key: 'priorityImportance',
      width: 100,
      render: (priority: Priority) => {
        const config = PRIORITY_CONFIG[priority]
        return <Tag color={config.color}>{config.label}</Tag>
      },
    },
    {
      title: '紧急程度',
      dataIndex: 'priorityUrgency',
      key: 'priorityUrgency',
      width: 100,
      render: (priority: Priority) => {
        const config = PRIORITY_CONFIG[priority]
        return <Tag color={config.color}>{config.label}</Tag>
      },
    },
    {
      title: '负责人',
      dataIndex: 'assignee',
      key: 'assignee',
      width: 120,
      render: (assignee: User) => assignee?.nickname || assignee?.username || '-',
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      fixed: 'right' as const,
      render: (_, record: Requirement) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                // TODO: 实现查看详情功能
                message.info('查看详情功能待实现')
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              size="small"
              onClick={() => handleEditRequirement(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个需求吗？"
              description="删除后无法恢复，请谨慎操作。"
              onConfirm={() => handleDeleteRequirement(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ]

  return (
    <div>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>需求管理</Title>
        <Text type="secondary">管理项目需求，跟踪需求状态和进度</Text>
      </div>

      {/* 搜索区域 */}
      <Card style={{ marginBottom: 24 }}>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="status" label="状态">
            <Select placeholder="选择状态" allowClear style={{ width: 120 }}>
              {Object.entries(REQUIREMENT_STATUS_CONFIG).map(([value, config]) => (
                <Option key={value} value={value}>
                  {config.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="assigneeId" label="负责人">
            <Select placeholder="选择负责人" allowClear style={{ width: 150 }}>
              {users?.map((user) => (
                <Option key={user.id} value={user.id}>
                  {user.nickname || user.username}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleResetSearch} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      {/* 操作区域 */}
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <div>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setIsCreateModalVisible(true)}
          >
            创建需求
          </Button>
        </div>
        <div>
          <Text type="secondary">
            共 {requirementData?.total || 0} 条记录
          </Text>
        </div>
      </div>

      {/* 需求列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={requirementData?.requirements || []}
          rowKey="id"
          loading={isLoading}
          pagination={{
            current: searchParams.page,
            pageSize: searchParams.pageSize,
            total: requirementData?.total || 0,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setSearchParams({ ...searchParams, page, pageSize: pageSize || 20 })
            },
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 创建需求模态框 */}
      <Modal
        title="创建需求"
        open={isCreateModalVisible}
        onCancel={() => setIsCreateModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateRequirement}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="title"
                label="需求标题"
                rules={[{ required: true, message: '请输入需求标题' }]}
              >
                <Input placeholder="请输入需求标题" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="businessDescription"
                label="业务描述"
                rules={[{ required: true, message: '请输入业务描述' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入面向业务人员的需求描述"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="acceptanceCriteria" label="验收标准">
                <TextArea
                  rows={3}
                  placeholder="请输入验收标准"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priorityImportance"
                label="重要程度"
                initialValue="MEDIUM"
              >
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="priorityUrgency"
                label="紧急程度"
                initialValue="MEDIUM"
              >
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="estimatedValue" label="预估价值">
                <Input placeholder="请输入预估价值或收益" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="targetUsers" label="目标用户">
                <Input placeholder="请输入目标用户群体" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="businessGoal" label="业务目标">
                <TextArea
                  rows={2}
                  placeholder="请输入业务目标"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="assigneeId" label="负责人">
                <Select placeholder="选择负责人" allowClear>
                  {users?.map((user) => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname || user.username}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="expectedDeliveryDate" label="期望交付时间">
                <DatePicker
                  showTime
                  placeholder="选择期望交付时间"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setIsCreateModalVisible(false)}>
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={createMutation.isPending}
              >
                创建
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑需求模态框 */}
      <Modal
        title="编辑需求"
        open={isEditModalVisible}
        onCancel={() => {
          setIsEditModalVisible(false)
          setEditingRequirement(null)
          editForm.resetFields()
        }}
        footer={null}
        width={800}
      >
        <Form
          form={editForm}
          layout="vertical"
          onFinish={handleUpdateRequirement}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="title"
                label="需求标题"
                rules={[{ required: true, message: '请输入需求标题' }]}
              >
                <Input placeholder="请输入需求标题" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="businessDescription"
                label="业务描述"
                rules={[{ required: true, message: '请输入业务描述' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入面向业务人员的需求描述"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="acceptanceCriteria" label="验收标准">
                <TextArea
                  rows={3}
                  placeholder="请输入验收标准"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="status" label="状态">
                <Select>
                  {Object.entries(REQUIREMENT_STATUS_CONFIG).map(([value, config]) => (
                    <Option key={value} value={value}>
                      {config.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="priorityImportance" label="重要程度">
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="priorityUrgency" label="紧急程度">
                <Select>
                  <Option value="HIGH">高</Option>
                  <Option value="MEDIUM">中</Option>
                  <Option value="LOW">低</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="estimatedValue" label="预估价值">
                <Input placeholder="请输入预估价值或收益" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="targetUsers" label="目标用户">
                <Input placeholder="请输入目标用户群体" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item name="businessGoal" label="业务目标">
                <TextArea
                  rows={2}
                  placeholder="请输入业务目标"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item name="assigneeId" label="负责人">
                <Select placeholder="选择负责人" allowClear>
                  {users?.map((user) => (
                    <Option key={user.id} value={user.id}>
                      {user.nickname || user.username}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="expectedDeliveryDate" label="期望交付时间">
                <DatePicker
                  showTime
                  placeholder="选择期望交付时间"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item name="actualDeliveryDate" label="实际交付时间">
                <DatePicker
                  showTime
                  placeholder="选择实际交付时间"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
            <Space>
              <Button
                onClick={() => {
                  setIsEditModalVisible(false)
                  setEditingRequirement(null)
                  editForm.resetFields()
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={updateMutation.isPending}
              >
                更新
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default RequirementsPage
