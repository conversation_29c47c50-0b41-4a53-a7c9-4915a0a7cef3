// 用户相关类型
export interface User {
  id: number
  username: string
  nickname?: string
  avatarUrl?: string
  userType: 'NORMAL' | 'SUPER_ADMIN'
  status: 'ACTIVE' | 'DISABLED' | 'LOCKED'
  failedLoginCount: number
  lockedUntil?: string
  createdAt: string
  updatedAt: string
}

export interface LoginRequest {
  username: string
  password: string
}

export interface RegisterRequest {
  username: string
  password: string
  nickname?: string
}

export interface LoginResponse {
  token: string
  user: User
}

export interface UserUpdateRequest {
  nickname?: string
  avatarUrl?: string
}

export interface PasswordChangeRequest {
  oldPassword: string
  newPassword: string
}

// 任务相关类型
export type TaskStatus = 
  | 'REQUIREMENT_CREATED'    // 需求创建
  | 'TASK_BREAKDOWN'         // 分解任务
  | 'IN_DEVELOPMENT'         // 开发中
  | 'PAUSED'                 // 暂停
  | 'TESTING'                // 测试
  | 'PENDING_RELEASE'        // 待上线
  | 'RELEASED'               // 上线

export type Priority = 'HIGH' | 'MEDIUM' | 'LOW'

export interface Task {
  id: number
  title: string
  description?: string
  requirementDescription?: string
  status: TaskStatus
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedHours?: number
  actualHours?: number
  creatorId: number
  assigneeId?: number
  parentTaskId?: number
  dueDate?: string
  startedAt?: string
  completedAt?: string
  createdAt: string
  updatedAt: string
  creator?: User
  assignee?: User
  parentTask?: Task
  subTasks?: Task[]
}

export interface TaskCreateRequest {
  title: string
  description?: string
  requirementDescription?: string
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedHours?: number
  assigneeId?: number
  parentTaskId?: number
  dueDate?: string
}

export interface TaskUpdateRequest {
  title?: string
  description?: string
  requirementDescription?: string
  status?: TaskStatus
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedHours?: number
  actualHours?: number
  assigneeId?: number
  dueDate?: string
}

export interface TaskListResponse {
  tasks: Task[]
  total: number
  page: number
  pageSize: number
}

export interface TaskStatusUpdateRequest {
  status: TaskStatus
  comment?: string
}

// 任务日志类型
export type ActionType = 
  | 'CREATE'
  | 'UPDATE_STATUS'
  | 'UPDATE_ASSIGNEE'
  | 'UPDATE_PRIORITY'
  | 'ADD_COMMENT'
  | 'UPLOAD_ATTACHMENT'

export interface TaskLog {
  id: number
  taskId: number
  userId: number
  actionType: ActionType
  oldValue?: string
  newValue?: string
  comment?: string
  createdAt: string
  user?: User
}

// 评论类型
export type CommentType = 'COMMENT' | 'BUG_REPORT'

export interface TaskComment {
  id: number
  taskId: number
  userId: number
  content: string
  commentType: CommentType
  parentCommentId?: number
  createdAt: string
  user?: User
  replies?: TaskComment[]
}

// 周计划类型
export type WeeklyPlanStatus = 'PLANNING' | 'IN_PROGRESS' | 'COMPLETED'

export interface WeeklyPlan {
  id: number
  weekStartDate: string
  weekEndDate: string
  planName?: string
  status: WeeklyPlanStatus
  summary?: string
  createdBy: number
  createdAt: string
  updatedAt: string
  creator?: User
  tasks?: WeeklyPlanTask[]
}

export interface WeeklyPlanTask {
  id: number
  weeklyPlanId: number
  taskId: number
  plannedHours?: number
  isEmergencyInsertion: boolean
  createdAt: string
  task?: Task
}

// API 响应类型
export interface ApiResponse<T> {
  success: boolean
  message: string
  data?: T
  code: number
}

export interface ErrorResponse {
  success: false
  message: string
  code: number
  details?: string
}

// 分页类型
export interface PaginationParams {
  page: number
  pageSize: number
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  pageSize: number
}

// 四象限类型
export interface QuadrantData {
  urgentImportant: Task[]
  notUrgentImportant: Task[]
  urgentNotImportant: Task[]
  notUrgentNotImportant: Task[]
}

// 需求相关类型
export type RequirementStatus =
  | 'DRAFT'           // 草稿
  | 'REVIEW'          // 评审中
  | 'APPROVED'        // 已批准
  | 'IN_PROGRESS'     // 进行中
  | 'TESTING'         // 测试中
  | 'DELIVERED'       // 已交付
  | 'REJECTED'        // 已拒绝

export interface Requirement {
  id: number
  title: string
  businessDescription: string        // 业务描述，面向老板
  acceptanceCriteria?: string        // 验收标准
  status: RequirementStatus
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedValue?: string            // 预估价值/收益
  targetUsers?: string               // 目标用户群体
  businessGoal?: string              // 业务目标
  creatorId: number
  assigneeId?: number                // 需求负责人
  expectedDeliveryDate?: string      // 期望交付时间
  actualDeliveryDate?: string        // 实际交付时间
  createdAt: string
  updatedAt: string
  creator?: User
  assignee?: User
  tasks?: Task[]                     // 关联的任务列表
}

export interface RequirementCreateRequest {
  title: string
  businessDescription: string
  acceptanceCriteria?: string
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedValue?: string
  targetUsers?: string
  businessGoal?: string
  assigneeId?: number
  expectedDeliveryDate?: string
}

export interface RequirementUpdateRequest {
  title?: string
  businessDescription?: string
  acceptanceCriteria?: string
  status?: RequirementStatus
  priorityImportance?: Priority
  priorityUrgency?: Priority
  estimatedValue?: string
  targetUsers?: string
  businessGoal?: string
  assigneeId?: number
  expectedDeliveryDate?: string
  actualDeliveryDate?: string
}

export interface RequirementListResponse {
  requirements: Requirement[]
  total: number
  page: number
  pageSize: number
}

// 需求分解为任务的请求
export interface RequirementBreakdownRequest {
  requirementId: number
  tasks: TaskBreakdownItem[]
}

export interface TaskBreakdownItem {
  title: string
  description?: string
  requirementDescription?: string
  priorityImportance: Priority
  priorityUrgency: Priority
  estimatedHours?: number
  assigneeId?: number
  dependencies?: number[]
}

// 业务价值报告（给老板看的）
export interface BusinessValueReport {
  requirementId: number
  title: string
  businessDescription: string
  targetUsers?: string
  businessGoal?: string
  estimatedValue?: string
  status: RequirementStatus
  progressPercentage: number
  expectedDeliveryDate?: string
  actualDeliveryDate?: string
  keyMilestones: Milestone[]
  risks: string[]
  achievements: string[]
}

export interface Milestone {
  name: string
  description: string
  targetDate: string
  actualDate?: string
  status: string // PENDING, COMPLETED, DELAYED
}

// 看板数据类型
export interface KanbanData {
  todo: Task[]
  inProgress: Task[]
  testing: Task[]
  done: Task[]
}

// 需求看板数据类型
export interface RequirementKanbanData {
  draft: Requirement[]
  review: Requirement[]
  approved: Requirement[]
  inProgress: Requirement[]
  testing: Requirement[]
  delivered: Requirement[]
  rejected: Requirement[]
}

// 任务统计类型
export interface TaskStatistics {
  total: number
  completed: number
  inProgress: number
  overdue: number
  thisWeekCompleted: number
  thisWeekCreated: number
}
