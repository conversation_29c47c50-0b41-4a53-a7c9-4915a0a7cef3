*.iml
android/.gradle
.gradle/
/local.properties
.idea/
*/.idea/
android/app/build
android/build
/captures
.externalNativeBuild
.cxx
android/local.properties
.history/
.vscode/
# Node.js 相关
node_modules/
**/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.pnpm-debug.log*
appium-tests-python/venv_py39/
appium-tests-python/venv/
appium-tests-python/__pycache__/
appium-tests-python/**/__pycache__/
*.pyc
*.log

# 签名相关文件
android/keystore.properties
android/keystore/
*.jks
*.keystore
*.pepk

# iOS 相关
ios/build/
ios/DerivedData/
DerivedData/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/Pods/
ios/Podfile.lock

# Carthage
ios/Carthage/Build/
ios/Carthage/Checkouts/

# Swift Package Manager
.build/
.swiftpm/
Package.resolved
*.xcworkspace

# Xcode
*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3
*.xcuserstate
*.xcscmblueprint
*.xccheckout

# macOS
.DS_Store
.AppleDouble
.LSOverride

# idea
backend/.idea/
backend/.gradle
backend/build/
