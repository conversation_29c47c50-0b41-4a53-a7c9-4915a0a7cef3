package com.beefcake.services

import com.beefcake.database.tables.ActionType
import com.beefcake.database.tables.RequirementStatus
import com.beefcake.database.tables.TaskType
import com.beefcake.models.*
import com.beefcake.repositories.RequirementRepository
import com.beefcake.repositories.TaskLogRepository
import com.beefcake.repositories.TaskRepository
import com.beefcake.repositories.UserRepository
import org.slf4j.LoggerFactory
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

class RequirementService {
    private val requirementRepository = RequirementRepository()
    private val taskRepository = TaskRepository()
    private val taskLogRepository = TaskLogRepository()
    private val userRepository = UserRepository()
    private val logger = LoggerFactory.getLogger(RequirementService::class.java)
    
    suspend fun createRequirement(request: RequirementCreateRequest, creatorId: Long): Result<Requirement> {
        try {
            // 验证创建者存在
            val creator = userRepository.findById(creatorId)
                ?: return Result.failure(Exception("创建者不存在"))
            
            // 验证负责人存在（如果指定）
            if (request.assigneeId != null) {
                val assignee = userRepository.findById(request.assigneeId)
                    ?: return Result.failure(Exception("指定的负责人不存在"))
            }
            
            // 解析期望交付时间
            val expectedDeliveryDate = request.expectedDeliveryDate?.let { 
                try {
                    LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                } catch (e: Exception) {
                    return Result.failure(Exception("期望交付时间格式错误，请使用 ISO 格式"))
                }
            }
            
            // 创建需求
            val requirement = requirementRepository.create(
                title = request.title,
                businessDescription = request.businessDescription,
                acceptanceCriteria = request.acceptanceCriteria,
                priorityImportance = request.priorityImportance,
                priorityUrgency = request.priorityUrgency,
                estimatedValue = request.estimatedValue,
                targetUsers = request.targetUsers,
                businessGoal = request.businessGoal,
                creatorId = creatorId,
                assigneeId = request.assigneeId,
                expectedDeliveryDate = expectedDeliveryDate
            ) ?: return Result.failure(Exception("需求创建失败"))
            
            logger.info("用户 $creatorId 创建了需求: ${requirement.title}")
            return Result.success(requirement)
            
        } catch (e: Exception) {
            logger.error("创建需求失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun getRequirementById(id: Long): Requirement? {
        return requirementRepository.findById(id)
    }
    
    suspend fun updateRequirement(id: Long, request: RequirementUpdateRequest, userId: Long): Result<Requirement> {
        try {
            // 验证需求存在
            val existingRequirement = requirementRepository.findById(id)
                ?: return Result.failure(Exception("需求不存在"))
            
            // 验证权限（创建者或负责人可以修改）
            if (existingRequirement.creatorId != userId && existingRequirement.assigneeId != userId) {
                return Result.failure(Exception("权限不足"))
            }
            
            // 验证负责人存在（如果要修改）
            if (request.assigneeId != null) {
                val assignee = userRepository.findById(request.assigneeId)
                    ?: return Result.failure(Exception("指定的负责人不存在"))
            }
            
            // 解析时间
            val expectedDeliveryDate = request.expectedDeliveryDate?.let { 
                try {
                    LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                } catch (e: Exception) {
                    return Result.failure(Exception("期望交付时间格式错误，请使用 ISO 格式"))
                }
            }
            
            val actualDeliveryDate = request.actualDeliveryDate?.let { 
                try {
                    LocalDateTime.parse(it, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))
                } catch (e: Exception) {
                    return Result.failure(Exception("实际交付时间格式错误，请使用 ISO 格式"))
                }
            }
            
            // 更新需求
            val success = requirementRepository.update(
                id = id,
                title = request.title,
                businessDescription = request.businessDescription,
                acceptanceCriteria = request.acceptanceCriteria,
                status = request.status,
                priorityImportance = request.priorityImportance,
                priorityUrgency = request.priorityUrgency,
                estimatedValue = request.estimatedValue,
                targetUsers = request.targetUsers,
                businessGoal = request.businessGoal,
                assigneeId = request.assigneeId,
                expectedDeliveryDate = expectedDeliveryDate,
                actualDeliveryDate = actualDeliveryDate
            )
            
            if (!success) {
                return Result.failure(Exception("需求更新失败"))
            }
            
            val updatedRequirement = requirementRepository.findById(id)!!
            logger.info("用户 $userId 更新了需求 $id")
            return Result.success(updatedRequirement)
            
        } catch (e: Exception) {
            logger.error("更新需求失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun updateRequirementStatus(id: Long, status: RequirementStatus, userId: Long): Result<Requirement> {
        try {
            val existingRequirement = requirementRepository.findById(id)
                ?: return Result.failure(Exception("需求不存在"))
            
            // 验证权限
            if (existingRequirement.creatorId != userId && existingRequirement.assigneeId != userId) {
                return Result.failure(Exception("权限不足"))
            }
            
            val success = requirementRepository.updateStatus(id, status)
            if (!success) {
                return Result.failure(Exception("状态更新失败"))
            }
            
            val updatedRequirement = requirementRepository.findById(id)!!
            logger.info("用户 $userId 将需求 $id 状态更新为 $status")
            return Result.success(updatedRequirement)
            
        } catch (e: Exception) {
            logger.error("更新需求状态失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun deleteRequirement(id: Long, userId: Long): Result<Unit> {
        try {
            val requirement = requirementRepository.findById(id)
                ?: return Result.failure(Exception("需求不存在"))
            
            // 只有创建者可以删除需求
            if (requirement.creatorId != userId) {
                return Result.failure(Exception("只有需求创建者可以删除需求"))
            }
            
            // 检查是否有关联的任务
            val tasks = taskRepository.findByRequirementId(id)
            if (tasks.isNotEmpty()) {
                return Result.failure(Exception("该需求下还有任务，请先删除所有关联任务"))
            }
            
            val success = requirementRepository.delete(id)
            if (!success) {
                return Result.failure(Exception("需求删除失败"))
            }
            
            logger.info("用户 $userId 删除了需求 $id")
            return Result.success(Unit)
            
        } catch (e: Exception) {
            logger.error("删除需求失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun getRequirementList(
        status: RequirementStatus? = null,
        assigneeId: Long? = null,
        creatorId: Long? = null,
        page: Int = 1,
        pageSize: Int = 20
    ): RequirementListResponse {
        val (requirements, total) = requirementRepository.findAll(status, assigneeId, creatorId, page, pageSize)
        return RequirementListResponse(requirements, total, page, pageSize)
    }
    
    suspend fun getRequirementKanbanData(): RequirementKanbanData {
        return RequirementKanbanData(
            draft = requirementRepository.findByStatus(RequirementStatus.DRAFT),
            review = requirementRepository.findByStatus(RequirementStatus.REVIEW),
            approved = requirementRepository.findByStatus(RequirementStatus.APPROVED),
            inProgress = requirementRepository.findByStatus(RequirementStatus.IN_PROGRESS),
            testing = requirementRepository.findByStatus(RequirementStatus.TESTING),
            delivered = requirementRepository.findByStatus(RequirementStatus.DELIVERED)
        )
    }
    
    suspend fun breakdownRequirement(request: RequirementBreakdownRequest, userId: Long): Result<List<Task>> {
        try {
            // 验证需求存在
            val requirement = requirementRepository.findById(request.requirementId)
                ?: return Result.failure(Exception("需求不存在"))
            
            // 验证权限
            if (requirement.creatorId != userId && requirement.assigneeId != userId) {
                return Result.failure(Exception("权限不足"))
            }
            
            val createdTasks = mutableListOf<Task>()
            
            // 创建任务
            for (taskItem in request.tasks) {
                val taskCreateRequest = TaskCreateRequest(
                    title = taskItem.title,
                    description = taskItem.description,
                    requirementId = request.requirementId,
                    taskType = TaskType.valueOf(taskItem.taskType),
                    estimatedHours = taskItem.estimatedHours,
                    assigneeId = taskItem.assigneeId,
                    dueDate = taskItem.dueDate,
                    dependencies = taskItem.dependencies
                )
                
                val taskService = TaskService()
                val result = taskService.createTask(taskCreateRequest, userId)
                if (result.isSuccess) {
                    createdTasks.add(result.getOrNull()!!)
                } else {
                    return Result.failure(Exception("创建任务失败: ${result.exceptionOrNull()?.message}"))
                }
            }
            
            // 更新需求状态为进行中
            requirementRepository.updateStatus(request.requirementId, RequirementStatus.IN_PROGRESS)
            
            logger.info("用户 $userId 将需求 ${request.requirementId} 分解为 ${createdTasks.size} 个任务")
            return Result.success(createdTasks)
            
        } catch (e: Exception) {
            logger.error("需求分解失败", e)
            return Result.failure(e)
        }
    }
    
    suspend fun getBusinessValueReport(requirementId: Long): BusinessValueReport? {
        val requirement = requirementRepository.findById(requirementId) ?: return null
        val tasks = taskRepository.findByRequirementId(requirementId)
        
        val totalTasks = tasks.size
        val completedTasks = tasks.count { it.status == com.beefcake.database.tables.TaskStatus.DONE }
        val progressPercentage = if (totalTasks > 0) (completedTasks.toDouble() / totalTasks * 100) else 0.0
        
        return BusinessValueReport(
            requirementId = requirement.id,
            title = requirement.title,
            businessDescription = requirement.businessDescription,
            targetUsers = requirement.targetUsers,
            businessGoal = requirement.businessGoal,
            estimatedValue = requirement.estimatedValue,
            status = requirement.status,
            progressPercentage = progressPercentage,
            expectedDeliveryDate = requirement.expectedDeliveryDate,
            actualDeliveryDate = requirement.actualDeliveryDate,
            keyMilestones = emptyList(), // TODO: 实现里程碑功能
            risks = emptyList(), // TODO: 实现风险识别
            achievements = emptyList() // TODO: 实现成就记录
        )
    }
}
