)com.beefcake.database.tables.Requirements.com.beefcake.database.tables.RequirementStatus3com.beefcake.database.tables.RequirementAttachments,com.beefcake.database.tables.TaskAttachments*com.beefcake.database.tables.Notifications-com.beefcake.database.tables.NotificationType)com.beefcake.database.tables.TaskComments(com.beefcake.database.tables.CommentType%com.beefcake.database.tables.TaskLogs'com.beefcake.database.tables.ActionType"com.beefcake.database.tables.Tasks'com.beefcake.database.tables.TaskStatus%com.beefcake.database.tables.TaskType%com.beefcake.database.tables.Priority-com.beefcake.database.tables.TaskDependencies+com.beefcake.database.tables.DependencyType"com.beefcake.database.tables.Users%com.beefcake.database.tables.UserType'com.beefcake.database.tables.UserStatus(com.beefcake.database.tables.WeeklyPlans,com.beefcake.database.tables.WeeklyPlanTasks-com.beefcake.database.tables.WeeklyPlanStatus+com.beefcake.models.Requirement.$serializer8com.beefcake.models.RequirementCreateRequest.$serializer8com.beefcake.models.RequirementUpdateRequest.$serializer7com.beefcake.models.RequirementListResponse.$serializer;com.beefcake.models.RequirementBreakdownRequest.$serializer1com.beefcake.models.TaskBreakdownItem.$serializer3com.beefcake.models.RequirementProgress.$serializer3com.beefcake.models.BusinessValueReport.$serializer)com.beefcake.models.Milestone.$serializer$com.beefcake.models.Task.$serializer.com.beefcake.models.TaskDependency.$serializer1com.beefcake.models.TaskCreateRequest.$serializer1com.beefcake.models.TaskUpdateRequest.$serializer0com.beefcake.models.TaskListResponse.$serializer7com.beefcake.models.TaskStatusUpdateRequest.$serializer1com.beefcake.models.TaskAssignRequest.$serializer/com.beefcake.models.TaskQueryParams.$serializer,com.beefcake.models.QuadrantData.$serializer*com.beefcake.models.KanbanData.$serializer5com.beefcake.models.RequirementKanbanData.$serializer.com.beefcake.models.TaskStatistics.$serializer'com.beefcake.models.TaskLog.$serializer3com.beefcake.models.TaskLogListResponse.$serializer$com.beefcake.models.User.$serializer1com.beefcake.models.UserCreateRequest.$serializer0com.beefcake.models.UserLoginRequest.$serializer1com.beefcake.models.UserLoginResponse.$serializer1com.beefcake.models.UserUpdateRequest.$serializer5com.beefcake.models.PasswordChangeRequest.$serializer4com.beefcake.models.PasswordResetRequest.$serializer0com.beefcake.models.UserListResponse.$serializer*com.beefcake.utils.ApiResponse.$serializer,com.beefcake.utils.ErrorResponse.$serializer/com.beefcake.models.UserAllResponse.$serializer1com.beefcake.models.RequirementOption.$serializer*com.beefcake.models.TaskOption.$serializer                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       