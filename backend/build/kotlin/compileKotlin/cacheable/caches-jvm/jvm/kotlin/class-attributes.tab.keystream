%com.beefcake.database.DatabaseFactory)com.beefcake.database.tables.Requirements.com.beefcake.database.tables.RequirementStatus3com.beefcake.database.tables.RequirementAttachments,com.beefcake.database.tables.TaskAttachments*com.beefcake.database.tables.Notifications-com.beefcake.database.tables.NotificationType)com.beefcake.database.tables.TaskComments(com.beefcake.database.tables.CommentType%com.beefcake.database.tables.TaskLogs'com.beefcake.database.tables.ActionType"com.beefcake.database.tables.Tasks'com.beefcake.database.tables.TaskStatus%com.beefcake.database.tables.TaskType%com.beefcake.database.tables.Priority-com.beefcake.database.tables.TaskDependencies+com.beefcake.database.tables.DependencyType"com.beefcake.database.tables.Users%com.beefcake.database.tables.UserType'com.beefcake.database.tables.UserStatus(com.beefcake.database.tables.WeeklyPlans,com.beefcake.database.tables.WeeklyPlanTasks-com.beefcake.database.tables.WeeklyPlanStatuscom.beefcake.models.Requirement)com.beefcake.models.Requirement.Companion+com.beefcake.models.Requirement.$serializer,com.beefcake.models.RequirementCreateRequest6com.beefcake.models.RequirementCreateRequest.Companion8com.beefcake.models.RequirementCreateRequest.$serializer,com.beefcake.models.RequirementUpdateRequest6com.beefcake.models.RequirementUpdateRequest.Companion8com.beefcake.models.RequirementUpdateRequest.$serializer+com.beefcake.models.RequirementListResponse5com.beefcake.models.RequirementListResponse.Companion7com.beefcake.models.RequirementListResponse.$serializer/com.beefcake.models.RequirementBreakdownRequest9com.beefcake.models.RequirementBreakdownRequest.Companion;com.beefcake.models.RequirementBreakdownRequest.$serializer%com.beefcake.models.TaskBreakdownItem/com.beefcake.models.TaskBreakdownItem.Companion1com.beefcake.models.TaskBreakdownItem.$serializer'com.beefcake.models.RequirementProgress1com.beefcake.models.RequirementProgress.Companion3com.beefcake.models.RequirementProgress.$serializer'com.beefcake.models.BusinessValueReport1com.beefcake.models.BusinessValueReport.Companion3com.beefcake.models.BusinessValueReport.$serializercom.beefcake.models.Milestone'com.beefcake.models.Milestone.Companion)com.beefcake.models.Milestone.$serializercom.beefcake.models.Task"com.beefcake.models.Task.Companion$com.beefcake.models.Task.$serializer"com.beefcake.models.TaskDependency,com.beefcake.models.TaskDependency.Companion.com.beefcake.models.TaskDependency.$serializer%com.beefcake.models.TaskCreateRequest/com.beefcake.models.TaskCreateRequest.Companion1com.beefcake.models.TaskCreateRequest.$serializer%com.beefcake.models.TaskUpdateRequest/com.beefcake.models.TaskUpdateRequest.Companion1com.beefcake.models.TaskUpdateRequest.$serializer$com.beefcake.models.TaskListResponse.com.beefcake.models.TaskListResponse.Companion0com.beefcake.models.TaskListResponse.$serializer+com.beefcake.models.TaskStatusUpdateRequest5com.beefcake.models.TaskStatusUpdateRequest.Companion7com.beefcake.models.TaskStatusUpdateRequest.$serializer%com.beefcake.models.TaskAssignRequest/com.beefcake.models.TaskAssignRequest.Companion1com.beefcake.models.TaskAssignRequest.$serializer#com.beefcake.models.TaskQueryParams-com.beefcake.models.TaskQueryParams.Companion/com.beefcake.models.TaskQueryParams.$serializer com.beefcake.models.QuadrantData*com.beefcake.models.QuadrantData.Companion,com.beefcake.models.QuadrantData.$serializercom.beefcake.models.KanbanData(com.beefcake.models.KanbanData.Companion*com.beefcake.models.KanbanData.$serializer)com.beefcake.models.RequirementKanbanData3com.beefcake.models.RequirementKanbanData.Companion5com.beefcake.models.RequirementKanbanData.$serializer"com.beefcake.models.TaskStatistics,com.beefcake.models.TaskStatistics.Companion.com.beefcake.models.TaskStatistics.$serializercom.beefcake.models.TaskLog%com.beefcake.models.TaskLog.Companion'com.beefcake.models.TaskLog.$serializer'com.beefcake.models.TaskLogListResponse1com.beefcake.models.TaskLogListResponse.Companion3com.beefcake.models.TaskLogListResponse.$serializercom.beefcake.models.User"com.beefcake.models.User.Companion$com.beefcake.models.User.$serializer%com.beefcake.models.UserCreateRequest/com.beefcake.models.UserCreateRequest.Companion1com.beefcake.models.UserCreateRequest.$serializer$com.beefcake.models.UserLoginRequest.com.beefcake.models.UserLoginRequest.Companion0com.beefcake.models.UserLoginRequest.$serializer%com.beefcake.models.UserLoginResponse/com.beefcake.models.UserLoginResponse.Companion1com.beefcake.models.UserLoginResponse.$serializer%com.beefcake.models.UserUpdateRequest/com.beefcake.models.UserUpdateRequest.Companion1com.beefcake.models.UserUpdateRequest.$serializer)com.beefcake.models.PasswordChangeRequest3com.beefcake.models.PasswordChangeRequest.Companion5com.beefcake.models.PasswordChangeRequest.$serializer(com.beefcake.models.PasswordResetRequest2com.beefcake.models.PasswordResetRequest.Companion4com.beefcake.models.PasswordResetRequest.$serializer$com.beefcake.models.UserListResponse.com.beefcake.models.UserListResponse.Companion0com.beefcake.models.UserListResponse.$serializer/com.beefcake.repositories.RequirementRepository+com.beefcake.repositories.TaskLogRepository(com.beefcake.repositories.TaskRepository(com.beefcake.repositories.UserRepository com.beefcake.utils.ResponseUtils(com.beefcake.services.RequirementService!com.beefcake.services.TaskService!com.beefcake.services.UserService+com.beefcake.services.UserService.Companioncom.beefcake.utils.JwtUtils com.beefcake.utils.PasswordUtils+com.beefcake.utils.PasswordValidationResult+com.beefcake.utils.UsernameValidationResultcom.beefcake.utils.ApiResponse(com.beefcake.utils.ApiResponse.Companion*com.beefcake.utils.ApiResponse.$serializer com.beefcake.utils.ErrorResponse*com.beefcake.utils.ErrorResponse.Companion,com.beefcake.utils.ErrorResponse.$serializer#com.beefcake.models.UserAllResponse-com.beefcake.models.UserAllResponse.Companion/com.beefcake.models.UserAllResponse.$serializer%com.beefcake.models.RequirementOption/com.beefcake.models.RequirementOption.Companion1com.beefcake.models.RequirementOption.$serializercom.beefcake.models.TaskOption(com.beefcake.models.TaskOption.Companion*com.beefcake.models.TaskOption.$serializer2com.beefcake.repositories.TaskDependencyRepository                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           