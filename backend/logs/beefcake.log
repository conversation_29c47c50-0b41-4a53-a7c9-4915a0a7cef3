2025-07-16 10:48:20.164 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:48:20.407 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:48:20.483 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:48:20.489 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:48:20.500 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Schema history table `beefcake`.`flyway_schema_history` does not exist yet
2025-07-16 10:48:20.501 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.006s)
2025-07-16 10:48:20.514 [main] INFO  o.f.c.i.s.JdbcTableSchemaHistory - Creating Schema History table `beefcake`.`flyway_schema_history` ...
2025-07-16 10:48:20.546 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: << Empty Schema >>
2025-07-16 10:48:20.560 [main] INFO  o.f.core.internal.command.DbMigrate - Migrating schema `beefcake` to version "1 - Create initial tables"
2025-07-16 10:48:20.776 [main] INFO  o.f.core.internal.command.DbMigrate - Successfully applied 1 migration to schema `beefcake`, now at version v1 (execution time 00:00.194s)
2025-07-16 10:48:20.784 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:48:20.784 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:48:20.844 [main] INFO  Application - Application started in 0.691 seconds.
2025-07-16 10:48:20.844 [main] INFO  Application - Application started: io.ktor.server.application.Application@a098d76
2025-07-16 10:48:20.910 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:51:35.410 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@a098d76
2025-07-16 10:51:35.412 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@a098d76
2025-07-16 10:51:36.321 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:51:36.492 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:51:36.549 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:51:36.555 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:51:36.570 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.011s)
2025-07-16 10:51:36.577 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:51:36.578 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:51:36.582 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:51:36.582 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:51:36.617 [main] INFO  Application - Application started in 0.307 seconds.
2025-07-16 10:51:36.617 [main] INFO  Application - Application started: io.ktor.server.application.Application@4760f169
2025-07-16 10:51:36.661 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:54:59.052 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4760f169
2025-07-16 10:54:59.054 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4760f169
2025-07-16 10:55:00.702 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:55:00.827 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:55:00.878 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6dc1484
2025-07-16 10:55:00.879 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:55:00.954 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:55:00.969 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:55:00.975 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:55:00.990 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.011s)
2025-07-16 10:55:00.997 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:55:00.998 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:55:01.002 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:55:01.002 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:55:01.031 [main] INFO  Application - Application started in 0.34 seconds.
2025-07-16 10:55:01.031 [main] INFO  Application - Application started: io.ktor.server.application.Application@d5d5353
2025-07-16 10:55:01.106 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:55:11.212 [eventLoopGroupProxy-4-1] INFO  com.beefcake.services.UserService - 用户注册成功: test
2025-07-16 10:55:11.222 [eventLoopGroupProxy-4-1] INFO  Application - 201 Created: POST /api/auth/register - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:34.793 [eventLoopGroupProxy-4-2] INFO  com.beefcake.services.UserService - 用户登录成功: test
2025-07-16 10:55:34.795 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: POST /api/auth/login - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:34.890 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:41.422 [eventLoopGroupProxy-4-4] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:41.429 [eventLoopGroupProxy-4-4] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:42.454 [eventLoopGroupProxy-4-5] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:42.456 [eventLoopGroupProxy-4-5] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:45.223 [eventLoopGroupProxy-4-6] ERROR TaskRoutes - 获取任务列表异常
java.lang.IllegalStateException: DESIGN of kotlin.String is not valid for enum TaskType
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.TaskRepository.resultRowToTask(TaskRepository.kt:209)
	at com.beefcake.repositories.TaskRepository.access$resultRowToTask(TaskRepository.kt:12)
	at com.beefcake.repositories.TaskRepository$findAll$2.invokeSuspend(TaskRepository.kt:151)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.repositories.TaskRepository$findAll$2.invoke(TaskRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:62)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 10:55:45.224 [eventLoopGroupProxy-4-6] INFO  Application - 400 Bad Request: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:55:45.731 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:56:46.588 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@d5d5353
2025-07-16 10:56:46.589 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@d5d5353
2025-07-16 10:56:48.367 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:56:48.467 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:56:48.529 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 10:56:48.529 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:56:48.623 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:56:48.624 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:56:48.624 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:56:48.639 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:56:48.644 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:56:48.663 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.014s)
2025-07-16 10:56:48.668 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:56:48.670 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:56:48.673 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:56:48.673 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:56:48.701 [main] INFO  Application - Application started in 0.35 seconds.
2025-07-16 10:56:48.702 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:56:48.771 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 10:56:55.969 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 10:59:19.394 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:19.397 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:20.719 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 10:59:20.836 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 10:59:20.896 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 10:59:20.897 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 10:59:20.983 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 10:59:20.998 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 10:59:21.004 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 10:59:21.021 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 10:59:21.028 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 10:59:21.029 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 10:59:21.033 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 10:59:21.033 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 10:59:21.085 [main] INFO  Application - Application started in 0.376 seconds.
2025-07-16 10:59:21.086 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 10:59:21.179 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:14:12.161 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:14:32.976 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:19:20.843 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:11.018 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:12.895 [eventLoopGroupProxy-4-6] ERROR RequirementRoutes - 获取需求列表异常
java.lang.IllegalStateException: APPROVED of kotlin.String is not valid for enum RequirementStatus
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.RequirementRepository.resultRowToRequirement(RequirementRepository.kt:168)
	at com.beefcake.repositories.RequirementRepository.access$resultRowToRequirement(RequirementRepository.kt:11)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invokeSuspend(RequirementRepository.kt:78)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invoke(RequirementRepository.kt)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invoke(RequirementRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:59)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 11:20:12.904 [eventLoopGroupProxy-4-6] INFO  Application - 400 Bad Request: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:12.907 [eventLoopGroupProxy-4-5] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:12.907 [eventLoopGroupProxy-4-5] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:13.920 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:13.921 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:13.928 [eventLoopGroupProxy-4-7] ERROR RequirementRoutes - 获取需求列表异常
java.lang.IllegalStateException: APPROVED of kotlin.String is not valid for enum RequirementStatus
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:876)
	at org.jetbrains.exposed.sql.EnumerationColumnType.valueFromDB(ColumnType.kt:865)
	at org.jetbrains.exposed.sql.ResultRow.rawToColumnValue(ResultRow.kt:82)
	at org.jetbrains.exposed.sql.ResultRow.access$rawToColumnValue(ResultRow.kt:8)
	at org.jetbrains.exposed.sql.ResultRow$getInternal$result$1$1.invoke(ResultRow.kt:69)
	at org.jetbrains.exposed.sql.vendors.DatabaseDialectKt.withDialect(DatabaseDialect.kt:189)
	at org.jetbrains.exposed.sql.ResultRow.getInternal(ResultRow.kt:68)
	at org.jetbrains.exposed.sql.ResultRow.get(ResultRow.kt:24)
	at com.beefcake.repositories.RequirementRepository.resultRowToRequirement(RequirementRepository.kt:168)
	at com.beefcake.repositories.RequirementRepository.access$resultRowToRequirement(RequirementRepository.kt:11)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invokeSuspend(RequirementRepository.kt:78)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invoke(RequirementRepository.kt)
	at com.beefcake.repositories.RequirementRepository$findAll$2.invoke(RequirementRepository.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invokeSuspend(DatabaseFactory.kt:59)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at com.beefcake.database.DatabaseFactory$dbQuery$2.invoke(DatabaseFactory.kt)
	at org.jetbrains.exposed.sql.transactions.experimental.SuspendedKt$suspendedTransactionAsyncInternal$1.invokeSuspend(Suspended.kt:187)
	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:104)
	at kotlinx.coroutines.internal.LimitedDispatcher$Worker.run(LimitedDispatcher.kt:111)
	at kotlinx.coroutines.scheduling.TaskImpl.run(Tasks.kt:99)
	at kotlinx.coroutines.scheduling.CoroutineScheduler.runSafely(CoroutineScheduler.kt:585)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.executeTask(CoroutineScheduler.kt:802)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.runWorker(CoroutineScheduler.kt:706)
	at kotlinx.coroutines.scheduling.CoroutineScheduler$Worker.run(CoroutineScheduler.kt:693)
2025-07-16 11:20:13.930 [eventLoopGroupProxy-4-7] INFO  Application - 400 Bad Request: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:20:59.928 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:20:59.930 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:21:01.643 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:21:01.746 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:21:01.824 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 11:21:01.825 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:21:01.898 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:21:01.898 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:21:01.899 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:21:01.914 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:21:01.920 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:21:01.937 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 11:21:01.944 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:21:01.945 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:21:01.949 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:21:01.949 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:21:01.987 [main] INFO  Application - Application started in 0.353 seconds.
2025-07-16 11:21:01.988 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:21:02.073 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:21:32.003 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:21:32.007 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:21:33.263 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:21:33.365 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:21:33.437 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d467c87
2025-07-16 11:21:33.438 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:21:33.508 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:21:33.509 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:21:33.509 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:21:33.523 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:21:33.529 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:21:33.544 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.012s)
2025-07-16 11:21:33.550 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:21:33.551 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:21:33.556 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:21:33.556 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:21:33.592 [main] INFO  Application - Application started in 0.338 seconds.
2025-07-16 11:21:33.592 [main] INFO  Application - Application started: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:21:33.665 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:22:00.099 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:00.100 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:00.144 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:01.120 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:01.121 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:04.085 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:05.204 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:05.205 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:05.210 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:06.220 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:06.221 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:14.289 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:14.290 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:14.293 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:15.303 [eventLoopGroupProxy-4-10] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:15.304 [eventLoopGroupProxy-4-10] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:22.538 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:22.539 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:22.543 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:23.562 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:23.562 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:46.942 [eventLoopGroupProxy-4-4] INFO  com.beefcake.services.UserService - 用户登录成功: test
2025-07-16 11:22:46.944 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: POST /api/auth/login - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:47.020 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:51.085 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:51.085 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:51.088 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:52.099 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:52.099 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:59.419 [eventLoopGroupProxy-4-10] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:59.419 [eventLoopGroupProxy-4-10] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:22:59.422 [eventLoopGroupProxy-4-9] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:23:00.442 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:23:00.443 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:23:58.405 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/tasks - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:00.234 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:01.440 [eventLoopGroupProxy-4-4] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:01.440 [eventLoopGroupProxy-4-4] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:01.443 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:02.456 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:02.457 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:12.144 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:12.145 [eventLoopGroupProxy-4-7] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:12.148 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:13.170 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:24:13.171 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:05.826 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:25:05.827 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@aaa0f76
2025-07-16 11:25:06.724 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:25:06.834 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:25:06.836 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:25:06.839 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:25:06.840 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:25:06.841 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:25:06.842 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:25:06.843 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:25:06.843 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:25:06.900 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 11:25:06.901 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:25:07.002 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:25:07.008 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@54c920b5
2025-07-16 11:25:07.022 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:25:07.022 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:25:07.022 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:25:07.022 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:25:07.022 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:25:07.022 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:25:07.022 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:25:07.022 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:25:07.023 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:25:07.023 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@15f42a8e
2025-07-16 11:25:07.024 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:25:07.024 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:25:07.025 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:25:07.025 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:25:07.028 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:25:07.039 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@242b3c53
2025-07-16 11:25:07.046 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:25:07.046 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:25:07.046 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:25:07.046 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:25:07.047 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:25:07.051 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:25:07.052 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:25:07.054 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@75dbd5c1
2025-07-16 11:25:07.056 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:25:07.057 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:25:07.060 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:25:07.060 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:25:07.068 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6205d25d
2025-07-16 11:25:07.069 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 11:25:07.071 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:25:07.073 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:25:07.075 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:25:07.076 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:25:07.080 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 28 of 80M
2025-07-16 11:25:07.080 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:25:07.080 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:25:07.083 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7195ccb3
2025-07-16 11:25:07.097 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f8f7c8c
2025-07-16 11:25:07.110 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7115b3f4
2025-07-16 11:25:07.120 [main] INFO  Application - Application started in 0.406 seconds.
2025-07-16 11:25:07.120 [main] INFO  Application - Application started: io.ktor.server.application.Application@67064bdc
2025-07-16 11:25:07.122 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:25:07.125 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6057b19f
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:25:07.130 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:25:07.131 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:25:07.131 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:25:07.132 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:25:07.133 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:25:07.133 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:25:07.141 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@42d32954
2025-07-16 11:25:07.142 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:25:07.142 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:25:07.142 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:25:07.143 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:25:07.143 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:25:07.143 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:25:07.143 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:25:07.143 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:25:07.146 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:25:07.146 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:25:07.146 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:25:07.146 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:25:07.158 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d4b8983
2025-07-16 11:25:07.160 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:25:07.162 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:25:07.164 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:25:07.164 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:25:07.165 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:25:07.165 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:25:07.167 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:25:07.172 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5a42af3
2025-07-16 11:25:07.172 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 10895 (auto-detected)
2025-07-16 11:25:07.173 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:25:07.173 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:25:07.175 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:25:07.175 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:25:07.176 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:25:07.178 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:25:07.178 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:25:07.183 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:25:07.186 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:25:07.186 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:25:07.186 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:25:07.186 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6cededdf
2025-07-16 11:25:07.191 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:25:07.201 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2d82a36e
2025-07-16 11:25:07.213 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3fc40123
2025-07-16 11:25:07.228 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3374c46
2025-07-16 11:25:07.243 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3394cad
2025-07-16 11:25:07.257 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@314427a5
2025-07-16 11:25:07.272 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@38222ff3
2025-07-16 11:25:07.284 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:25:17.905 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:25:17.906 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:25:17.907 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@e433456
2025-07-16 11:25:17.930 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:25:17.930 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:25:17.930 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:25:17.930 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:25:17.931 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:25:17.977 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:17.977 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:17.980 [DefaultDispatcher-worker-2] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 11:25:18.007 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:25:18.009 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:25:18.021 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:18.996 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:18.997 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:25:37.006 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:25:37.007 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:26:07.012 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:26:07.013 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:26:09.810 [eventLoopGroupProxy-4-5] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:26:09.811 [eventLoopGroupProxy-4-5] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:26:09.814 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:26:09.816 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:26:09.818 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:26:10.836 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:26:10.836 [eventLoopGroupProxy-4-6] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:26:37.015 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:26:37.015 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:27:07.011 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:27:07.011 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:27:37.004 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:27:37.005 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:28:07.007 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:28:07.007 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:28:35.277 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:35.277 [eventLoopGroupProxy-4-8] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:35.278 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:28:35.280 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:28:35.281 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:36.292 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:36.293 [eventLoopGroupProxy-4-9] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:37.010 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:28:37.010 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:28:37.668 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:28:37.669 [eventLoopGroupProxy-4-10] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:37.669 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:28:37.671 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:38.692 [eventLoopGroupProxy-4-2] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:42.416 [eventLoopGroupProxy-4-3] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:42.417 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:28:42.418 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:28:42.420 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:28:43.441 [eventLoopGroupProxy-4-5] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:07.015 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:29:07.015 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-6
2025-07-16 11:29:23.176 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 11:29:23.177 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:23.178 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:24.933 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:29:25.027 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:29:25.029 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:29:25.032 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:29:25.033 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:29:25.034 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:29:25.034 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:29:25.034 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:29:25.034 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:29:25.034 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:29:25.035 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:29:25.036 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:29:25.037 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:29:25.038 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:29:25.038 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:29:25.038 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:29:25.038 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:29:25.038 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:29:25.038 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:29:25.091 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 11:29:25.091 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:29:25.180 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:29:25.180 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:29:25.180 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:29:25.181 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:29:25.181 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:29:25.181 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:29:25.181 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:29:25.183 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:29:25.196 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:29:25.199 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4690bdc0
2025-07-16 11:29:25.199 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:29:25.199 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:29:25.200 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:29:25.200 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:29:25.200 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:29:25.208 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:29:25.209 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:29:25.214 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:29:25.214 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7adb7fb
2025-07-16 11:29:25.215 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:29:25.220 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:29:25.221 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:29:25.230 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3660f2f8
2025-07-16 11:29:25.238 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.024s)
2025-07-16 11:29:25.241 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:29:25.247 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@678248b4
2025-07-16 11:29:25.250 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:29:25.254 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:29:25.256 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:29:25.264 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 27 of 80M
2025-07-16 11:29:25.264 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4a7c7598
2025-07-16 11:29:25.264 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:29:25.264 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:29:25.281 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@63cc5397
2025-07-16 11:29:25.297 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@64d877c5
2025-07-16 11:29:25.308 [main] INFO  Application - Application started in 0.393 seconds.
2025-07-16 11:29:25.308 [main] INFO  Application - Application started: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:25.310 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:29:25.314 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62805e
2025-07-16 11:29:25.317 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:29:25.317 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:29:25.317 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:29:25.317 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:29:25.317 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:29:25.318 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:29:25.318 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:29:25.318 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:29:25.318 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:29:25.319 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:29:25.319 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:29:25.326 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:29:25.326 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:29:25.329 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:29:25.329 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:29:25.329 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:29:25.329 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:29:25.330 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@629db154
2025-07-16 11:29:25.340 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:29:25.343 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:29:25.345 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:29:25.345 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:29:25.346 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:29:25.346 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:29:25.346 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e1ac2fb
2025-07-16 11:29:25.350 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:29:25.356 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11058 (auto-detected)
2025-07-16 11:29:25.357 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:29:25.357 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:29:25.359 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:29:25.360 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:29:25.361 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@407ca7dc
2025-07-16 11:29:25.361 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:29:25.363 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:29:25.363 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:29:25.370 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:29:25.373 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:29:25.374 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:29:25.374 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:29:25.376 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@74c7ae39
2025-07-16 11:29:25.381 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:29:25.392 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3bed018e
2025-07-16 11:29:25.411 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@cb50d74
2025-07-16 11:29:25.428 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@106ea9ff
2025-07-16 11:29:25.445 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ae0ef78
2025-07-16 11:29:25.463 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@449474cf
2025-07-16 11:29:25.480 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@333997b4
2025-07-16 11:29:25.496 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@751f374a
2025-07-16 11:29:25.509 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:29:29.816 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:29:29.816 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:29:29.816 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@273ed6fc
2025-07-16 11:29:29.842 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:29:29.843 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:29:29.843 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:29:29.843 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:29:29.843 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:29:29.891 [DefaultDispatcher-worker-2] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 11:29:29.917 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:29:29.917 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:29:29.920 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:29:29.920 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:29:29.945 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:29.945 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:33.244 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:29:33.244 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:29:33.246 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:29:33.246 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:29:33.248 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:33.248 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:42.638 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:29:42.638 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:29:42.639 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:29:42.639 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:29:42.641 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:42.641 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 11:29:54.220 [eventLoopGroupProxy-3-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-6
2025-07-16 11:29:55.198 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:29:55.198 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:29:55.264 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:55.265 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:56.593 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:29:56.715 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:29:56.718 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:29:56.720 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:29:56.721 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:29:56.722 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:29:56.723 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:29:56.723 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:29:56.724 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:29:56.725 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:29:56.726 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:29:56.788 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 11:29:56.789 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:29:56.860 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:29:56.860 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:29:56.860 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:29:56.860 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:29:56.861 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:29:56.861 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:29:56.861 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:29:56.861 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:29:56.862 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:29:56.875 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:29:56.875 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:29:56.876 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:29:56.876 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:29:56.876 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:29:56.880 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:29:56.881 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:29:56.884 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:29:56.885 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:29:56.888 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:29:56.888 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:29:56.894 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:29:56.896 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@373a119c
2025-07-16 11:29:56.898 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 11:29:56.900 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:29:56.903 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:29:56.904 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:29:56.906 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:29:56.909 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 28 of 80M
2025-07-16 11:29:56.910 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:29:56.910 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:29:56.911 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@76071ba7
2025-07-16 11:29:56.927 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@473b0db4
2025-07-16 11:29:56.943 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1628e4e8
2025-07-16 11:29:56.954 [main] INFO  Application - Application started in 0.37 seconds.
2025-07-16 11:29:56.954 [main] INFO  Application - Application started: io.ktor.server.application.Application@67064bdc
2025-07-16 11:29:56.956 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:29:56.958 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7b0fe386
2025-07-16 11:29:56.964 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:29:56.965 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:29:56.965 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:29:56.965 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:29:56.965 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:29:56.966 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:29:56.966 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:29:56.966 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:29:56.966 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:29:56.967 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:29:56.967 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:29:56.973 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@12661e79
2025-07-16 11:29:56.973 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:29:56.973 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:29:56.973 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:29:56.974 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:29:56.974 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:29:56.974 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:29:56.974 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:29:56.974 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:29:56.976 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:29:56.976 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:29:56.976 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:29:56.976 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:29:56.989 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@42431e11
2025-07-16 11:29:56.989 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:29:56.992 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:29:56.994 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:29:56.994 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:29:56.997 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:29:56.997 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:29:57.004 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:29:57.006 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6620329b
2025-07-16 11:29:57.014 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11080 (auto-detected)
2025-07-16 11:29:57.017 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:29:57.017 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:29:57.020 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:29:57.021 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:29:57.022 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:29:57.025 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5c49cda4
2025-07-16 11:29:57.027 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:29:57.028 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:29:57.038 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:29:57.042 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@443f9fce
2025-07-16 11:29:57.044 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:29:57.045 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:29:57.045 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:29:57.054 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:29:57.057 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@57efe11c
2025-07-16 11:29:57.070 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2a91508a
2025-07-16 11:29:57.085 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@e8ca9b3
2025-07-16 11:29:57.100 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@52e85a25
2025-07-16 11:29:57.116 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f126e63
2025-07-16 11:29:57.131 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@497d8e26
2025-07-16 11:29:57.151 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2024bc37
2025-07-16 11:29:57.175 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7ad4a109
2025-07-16 11:29:57.194 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e79067a
2025-07-16 11:29:57.207 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:30:17.569 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:30:17.569 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:30:17.569 [eventLoopGroupProxy-3-2] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@46c70365
2025-07-16 11:30:17.597 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:30:17.597 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:30:17.597 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:30:17.597 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:30:17.597 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:30:17.649 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 11:30:17.675 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:30:17.675 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:30:17.678 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:30:17.678 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:30:17.718 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:30:17.718 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:30:26.899 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:30:26.900 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:30:29.536 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:30:29.536 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:30:30.574 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@67064bdc
2025-07-16 11:30:30.575 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@67064bdc
2025-07-16 11:30:31.944 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:30:32.049 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:30:32.052 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:30:32.054 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:30:32.055 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:30:32.056 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:30:32.057 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:30:32.058 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:30:32.059 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:30:32.059 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:30:32.131 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@779de014
2025-07-16 11:30:32.132 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:30:32.212 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:30:32.212 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:30:32.212 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:30:32.212 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:30:32.213 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:30:32.213 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:30:32.214 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:30:32.228 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:30:32.228 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:30:32.228 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:30:32.228 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:30:32.228 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:30:32.232 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:30:32.232 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:30:32.234 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:30:32.235 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@ad96e68
2025-07-16 11:30:32.237 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:30:32.239 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:30:32.242 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:30:32.242 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:30:32.249 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4be46453
2025-07-16 11:30:32.251 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 11:30:32.253 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:30:32.257 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:30:32.259 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:30:32.261 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:30:32.265 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7ae5acdb
2025-07-16 11:30:32.267 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 39 of 80M
2025-07-16 11:30:32.267 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:30:32.267 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:30:32.280 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2bcb9b1c
2025-07-16 11:30:32.295 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3624db47
2025-07-16 11:30:32.304 [main] INFO  Application - Application started in 0.372 seconds.
2025-07-16 11:30:32.304 [main] INFO  Application - Application started: io.ktor.server.application.Application@1b956cfa
2025-07-16 11:30:32.306 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:30:32.310 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f854476
2025-07-16 11:30:32.313 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:30:32.313 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:30:32.314 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:30:32.315 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:30:32.315 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:30:32.315 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:30:32.316 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:30:32.316 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:30:32.316 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:30:32.316 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:30:32.317 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:30:32.317 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:30:32.317 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:30:32.317 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:30:32.319 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:30:32.319 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:30:32.319 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:30:32.319 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:30:32.325 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5ffda306
2025-07-16 11:30:32.329 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:30:32.331 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:30:32.332 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:30:32.332 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:30:32.334 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:30:32.334 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:30:32.337 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:30:32.342 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b56ed67
2025-07-16 11:30:32.349 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11104 (auto-detected)
2025-07-16 11:30:32.351 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:30:32.351 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:30:32.352 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:30:32.352 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:30:32.353 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:30:32.358 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b7a9f4a
2025-07-16 11:30:32.358 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:30:32.358 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:30:32.366 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:30:32.371 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:30:32.371 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:30:32.371 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:30:32.375 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@74dc81c1
2025-07-16 11:30:32.378 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:30:32.389 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d6fc04d
2025-07-16 11:30:32.404 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4ed0f132
2025-07-16 11:30:32.418 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@b145787
2025-07-16 11:30:32.435 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@13fab58c
2025-07-16 11:30:32.448 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6f3fb7e8
2025-07-16 11:30:32.464 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@400a6b08
2025-07-16 11:30:32.480 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7b384ccb
2025-07-16 11:30:32.497 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@39f60933
2025-07-16 11:30:32.514 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@38346737
2025-07-16 11:30:32.527 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:30:35.132 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:30:35.132 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:30:35.133 [eventLoopGroupProxy-3-2] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@41f59a0d
2025-07-16 11:30:35.147 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:30:35.147 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:30:35.147 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:30:35.147 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:30:35.147 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:30:35.201 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@779de014 due to dirty commit state on close().
2025-07-16 11:30:35.216 [eventLoopGroupProxy-4-1] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:30:35.234 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:30:35.236 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:30:35.247 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:30:36.234 [eventLoopGroupProxy-4-3] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:31:02.194 [eventLoopGroupProxy-4-5] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:31:02.195 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:31:02.198 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:31:02.200 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:31:02.236 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:31:02.237 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:31:03.212 [eventLoopGroupProxy-4-6] INFO  Application - 403 Forbidden: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:31:32.245 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:31:32.245 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:32:02.250 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:32:02.251 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:32:29.241 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.username = 'super'
2025-07-16 11:32:29.481 [DefaultDispatcher-worker-1] DEBUG Exposed - UPDATE users SET failed_login_count=0, locked_until=NULL, updated_at='2025-07-16 11:32:29.477435' WHERE users.id = 1
2025-07-16 11:32:29.484 [eventLoopGroupProxy-4-7] INFO  com.beefcake.services.UserService - 用户登录成功: super
2025-07-16 11:32:29.486 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: POST /api/auth/login - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:29.556 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM tasks
2025-07-16 11:32:29.558 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM tasks WHERE tasks.status = 'DONE'
2025-07-16 11:32:29.559 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM tasks WHERE tasks.status IN ('IN_PROGRESS', 'REVIEW', 'TESTING')
2025-07-16 11:32:29.560 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM tasks WHERE (tasks.due_date < '2025-07-16 11:32:29.559668') AND (tasks.status <> 'DONE')
2025-07-16 11:32:29.563 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/tasks/statistics - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:31.707 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:32:31.709 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:32:31.710 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:32:31.711 [eventLoopGroupProxy-4-10] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:31.712 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:32:31.720 [eventLoopGroupProxy-4-9] INFO  Application - 200 OK: GET /api/users/list - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:32.255 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:32:32.255 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:32:47.337 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:32:47.338 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:32:47.340 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:47.352 [eventLoopGroupProxy-4-2] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:47.352 [eventLoopGroupProxy-4-2] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:48.388 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:32:48.389 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:33:02.258 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:33:02.258 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:33:32.264 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:33:32.264 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:34:02.267 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:34:02.268 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:34:32.273 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:34:32.274 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:35:02.279 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:35:02.280 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:35:32.282 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:35:32.283 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:36:02.289 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:36:02.289 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:36:32.290 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:36:32.291 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:37:01.200 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 11:37:01.200 [eventLoopGroupProxy-3-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-6
2025-07-16 11:37:01.200 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:37:01.200 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:37:01.200 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 11:37:01.201 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 11:37:01.202 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@1b956cfa
2025-07-16 11:37:01.203 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@1b956cfa
2025-07-16 11:37:03.147 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:37:03.238 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:37:03.241 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:37:03.242 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:37:03.243 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:37:03.244 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:37:03.245 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:37:03.246 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:37:03.247 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:37:03.248 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:37:03.248 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:37:03.248 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:37:03.249 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:37:03.317 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@66908383
2025-07-16 11:37:03.318 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:37:03.403 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:37:03.403 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:37:03.403 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:37:03.403 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:37:03.404 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:37:03.404 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:37:03.404 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:37:03.404 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:37:03.405 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:37:03.421 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:37:03.421 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:37:03.421 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:37:03.422 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:37:03.422 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:37:03.423 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:37:03.425 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6dd6f667
2025-07-16 11:37:03.426 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:37:03.427 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:37:03.430 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:37:03.431 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:37:03.434 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:37:03.435 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:37:03.440 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2cebfc5c
2025-07-16 11:37:03.445 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.013s)
2025-07-16 11:37:03.446 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:37:03.451 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:37:03.453 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:37:03.454 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d8d5447
2025-07-16 11:37:03.455 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:37:03.459 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 39 of 80M
2025-07-16 11:37:03.460 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:37:03.460 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:37:03.467 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4cc40313
2025-07-16 11:37:03.479 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b46678c
2025-07-16 11:37:03.493 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@488abb7e
2025-07-16 11:37:03.494 [main] INFO  Application - Application started in 0.357 seconds.
2025-07-16 11:37:03.494 [main] INFO  Application - Application started: io.ktor.server.application.Application@77bb48d5
2025-07-16 11:37:03.496 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:37:03.502 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:37:03.503 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:37:03.504 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:37:03.504 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:37:03.504 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:37:03.505 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:37:03.506 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:37:03.506 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:37:03.507 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:37:03.507 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:37:03.507 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:37:03.507 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:37:03.508 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d62fbef
2025-07-16 11:37:03.517 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:37:03.519 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:37:03.520 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:37:03.520 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:37:03.522 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:37:03.522 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:37:03.523 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@13a4f62a
2025-07-16 11:37:03.524 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:37:03.530 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11286 (auto-detected)
2025-07-16 11:37:03.531 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:37:03.531 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:37:03.532 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:37:03.532 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:37:03.533 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:37:03.536 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:37:03.536 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:37:03.537 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5884f8d6
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:37:03.542 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:37:03.545 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:37:03.545 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:37:03.545 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:37:03.549 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:37:03.552 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@256a395c
2025-07-16 11:37:03.565 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@762fb2fd
2025-07-16 11:37:03.578 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4f3a0e79
2025-07-16 11:37:03.592 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@584d1636
2025-07-16 11:37:03.607 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62a98d7b
2025-07-16 11:37:03.626 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6ddee842
2025-07-16 11:37:03.641 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e91036a
2025-07-16 11:37:03.657 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@589d24e4
2025-07-16 11:37:03.672 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@12d2a356
2025-07-16 11:37:03.688 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b1af593
2025-07-16 11:37:03.701 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:37:12.238 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:37:12.238 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:37:12.239 [eventLoopGroupProxy-3-2] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@4070e4d1
2025-07-16 11:37:12.259 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:37:12.259 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:37:12.259 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:37:12.259 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:37:12.259 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:37:12.312 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@66908383 due to dirty commit state on close().
2025-07-16 11:37:12.318 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:12.318 [eventLoopGroupProxy-4-1] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:12.352 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:37:12.354 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:37:12.368 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:13.339 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:13.339 [eventLoopGroupProxy-4-3] INFO  Application - 404 Not Found: GET /api/users - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:15.478 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:37:15.479 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:37:15.482 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:15.483 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:37:15.485 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:37:15.490 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:37:33.429 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:37:33.429 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:38:03.430 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:38:03.430 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:38:33.432 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:38:33.433 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:39:03.437 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:39:03.438 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:39:33.439 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:39:33.440 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:39:35.121 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 11:39:35.121 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 11:39:35.121 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:39:35.121 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 11:39:35.121 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 2 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:39:36.165 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@77bb48d5
2025-07-16 11:39:36.167 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@77bb48d5
2025-07-16 11:40:19.836 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:40:19.932 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:40:19.935 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:40:19.938 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:40:19.939 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:40:19.940 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:40:19.941 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:40:19.942 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:40:19.943 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:40:19.943 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:40:20.008 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 11:40:20.009 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:40:20.087 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:40:20.088 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:40:20.088 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:40:20.088 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:40:20.088 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:40:20.089 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:40:20.089 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:40:20.103 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:40:20.104 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:40:20.104 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:40:20.104 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:40:20.104 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:40:20.110 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:40:20.111 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:40:20.111 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:40:20.113 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3e6553d8
2025-07-16 11:40:20.114 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:40:20.115 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:40:20.119 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:40:20.119 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:40:20.128 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7f5859e3
2025-07-16 11:40:20.128 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.014s)
2025-07-16 11:40:20.129 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:40:20.133 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:40:20.135 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:40:20.136 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:40:20.142 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 27 of 80M
2025-07-16 11:40:20.142 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:40:20.142 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:40:20.143 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4eed81e4
2025-07-16 11:40:20.156 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1f77a6ac
2025-07-16 11:40:20.170 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@706fe3a7
2025-07-16 11:40:20.184 [main] INFO  Application - Application started in 0.357 seconds.
2025-07-16 11:40:20.184 [main] INFO  Application - Application started: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 11:40:20.186 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b39dd72
2025-07-16 11:40:20.188 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:40:20.196 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:40:20.196 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:40:20.196 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:40:20.197 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:40:20.198 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:40:20.198 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:40:20.201 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3b111941
2025-07-16 11:40:20.204 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:40:20.204 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:40:20.204 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:40:20.205 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:40:20.205 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:40:20.205 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:40:20.205 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:40:20.205 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:40:20.207 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:40:20.207 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:40:20.207 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:40:20.207 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:40:20.217 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d4df660
2025-07-16 11:40:20.218 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:40:20.220 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:40:20.222 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:40:20.223 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:40:20.224 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:40:20.225 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:40:20.227 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:40:20.232 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7fadd907
2025-07-16 11:40:20.235 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11451 (auto-detected)
2025-07-16 11:40:20.237 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:40:20.237 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:40:20.239 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:40:20.239 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:40:20.240 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:40:20.243 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:40:20.243 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:40:20.246 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@16f24afd
2025-07-16 11:40:20.249 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:40:20.250 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:40:20.253 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:40:20.253 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:40:20.254 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:40:20.259 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6371b3b8
2025-07-16 11:40:20.260 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:40:20.274 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@71cfc1be
2025-07-16 11:40:20.288 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@445b51d0
2025-07-16 11:40:20.304 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@697505d5
2025-07-16 11:40:20.319 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5f8ebf00
2025-07-16 11:40:20.333 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4db811e6
2025-07-16 11:40:20.347 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@740adceb
2025-07-16 11:40:20.361 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62b96d71
2025-07-16 11:40:20.375 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e3a5527
2025-07-16 11:40:20.388 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:40:50.115 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:40:50.117 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:41:02.786 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:41:02.786 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:41:02.792 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@4865e6ec
2025-07-16 11:41:02.812 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:41:02.812 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:41:02.812 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:41:02.812 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:41:02.812 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:41:02.872 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 11:41:02.901 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:41:02.901 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:41:02.904 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:41:02.904 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:41:02.932 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:41:02.932 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:41:20.123 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:41:20.123 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:41:50.125 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:41:50.126 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:42:20.132 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:42:20.133 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:42:50.135 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:42:50.136 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:43:20.216 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:43:20.217 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:43:50.223 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:43:50.224 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:44:20.229 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:44:20.231 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:44:50.237 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:44:50.238 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:45:20.242 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:45:20.243 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:45:50.245 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:45:50.247 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:46:20.253 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:46:20.253 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:46:50.256 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:46:50.258 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:47:20.263 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:47:20.265 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:47:50.271 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:47:50.271 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:48:20.272 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:48:20.272 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:48:50.278 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:48:50.278 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:49:03.953 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:49:03.953 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:49:03.956 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:49:03.956 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:49:03.958 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:49:03.958 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:49:20.284 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:49:20.285 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:49:32.376 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 11:49:32.376 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 11:49:32.376 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 11:49:32.376 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 11:49:33.406 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 11:49:33.407 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 11:49:35.202 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 11:49:35.293 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 11:49:35.296 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:49:35.297 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:49:35.298 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:49:35.299 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:49:35.300 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 11:49:35.301 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 11:49:35.302 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 11:49:35.303 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 11:49:35.304 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 11:49:35.371 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5c41d037
2025-07-16 11:49:35.372 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 11:49:35.451 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 11:49:35.451 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 11:49:35.451 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 11:49:35.451 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 11:49:35.452 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 11:49:35.452 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 11:49:35.452 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 11:49:35.452 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 11:49:35.453 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 11:49:35.467 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 11:49:35.468 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 11:49:35.468 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 11:49:35.468 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 11:49:35.468 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 11:49:35.473 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 11:49:35.474 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 11:49:35.475 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 11:49:35.475 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@78c82bea
2025-07-16 11:49:35.478 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 11:49:35.479 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:49:35.482 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:49:35.482 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 11:49:35.489 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@39f521d1
2025-07-16 11:49:35.491 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.012s)
2025-07-16 11:49:35.492 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 11:49:35.494 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 11:49:35.496 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 11:49:35.498 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 11:49:35.502 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 38 of 80M
2025-07-16 11:49:35.503 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 11:49:35.503 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 11:49:35.503 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@8869a13
2025-07-16 11:49:35.518 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@59dade9e
2025-07-16 11:49:35.533 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@17efe12b
2025-07-16 11:49:35.537 [main] INFO  Application - Application started in 0.345 seconds.
2025-07-16 11:49:35.538 [main] INFO  Application - Application started: io.ktor.server.application.Application@3a66e67e
2025-07-16 11:49:35.540 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 11:49:35.547 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5d9463e5
2025-07-16 11:49:35.549 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 11:49:35.552 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 11:49:35.553 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 11:49:35.556 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 11:49:35.556 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 11:49:35.556 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 11:49:35.556 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 11:49:35.559 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 11:49:35.560 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 11:49:35.561 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 11:49:35.561 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 11:49:35.562 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@331bdb9d
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 11:49:35.562 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 11:49:35.563 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 11:49:35.563 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 11:49:35.564 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 11:49:35.564 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 11:49:35.564 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 11:49:35.565 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 11:49:35.576 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@346a8861
2025-07-16 11:49:35.578 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 11:49:35.580 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 11:49:35.581 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 11:49:35.581 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 11:49:35.582 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 11:49:35.582 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 11:49:35.584 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 11:49:35.590 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 11747 (auto-detected)
2025-07-16 11:49:35.591 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 11:49:35.591 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2e59ebb6
2025-07-16 11:49:35.591 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 11:49:35.592 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 11:49:35.592 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 11:49:35.592 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 11:49:35.595 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 11:49:35.595 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 11:49:35.601 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 11:49:35.602 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 11:49:35.604 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 11:49:35.604 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 11:49:35.604 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 11:49:35.605 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7e3366ee
2025-07-16 11:49:35.608 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 11:49:35.621 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44454d30
2025-07-16 11:49:35.635 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@69f0164e
2025-07-16 11:49:35.650 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22843e32
2025-07-16 11:49:35.666 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@335d9005
2025-07-16 11:49:35.687 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2606dd9a
2025-07-16 11:49:35.702 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@bd7917d
2025-07-16 11:49:35.718 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1aa6353a
2025-07-16 11:49:35.736 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@56918621
2025-07-16 11:49:35.753 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@111ad1ee
2025-07-16 11:49:35.766 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:49:39.186 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 11:49:39.186 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 11:49:39.186 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@8e213df
2025-07-16 11:49:39.202 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 11:49:39.202 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 11:49:39.202 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 11:49:39.202 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 11:49:39.202 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 11:49:39.250 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@5c41d037 due to dirty commit state on close().
2025-07-16 11:49:39.288 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:49:39.288 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:49:39.294 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:49:39.294 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:49:39.327 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:49:39.327 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:50:05.476 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:50:05.477 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:50:35.482 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:50:35.483 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:51:05.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:51:05.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:51:35.492 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:51:35.492 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:52:05.493 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:52:05.495 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 11:52:20.832 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM users
2025-07-16 11:52:20.832 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 11:52:20.834 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC LIMIT 20
2025-07-16 11:52:20.834 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 11:52:20.836 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:52:20.836 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 11:52:35.559 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 11:52:35.560 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:08:38.120 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m2s561ms).
2025-07-16 12:08:38.121 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:08:38.121 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:09:08.124 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:09:08.125 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:09:38.133 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:09:38.134 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:10:08.140 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:10:08.140 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:10:38.145 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:10:38.146 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:11:08.155 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:11:08.156 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:11:38.167 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:11:38.167 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:27:55.486 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m17s318ms).
2025-07-16 12:27:55.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:27:55.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:28:25.494 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:28:25.495 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:28:55.500 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:28:55.500 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:29:25.508 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:29:25.509 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:29:55.520 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:29:55.521 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:30:25.531 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:30:25.531 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:46:10.129 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=15m44s599ms).
2025-07-16 12:46:10.130 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:46:10.130 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:48:08.562 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m58s432ms).
2025-07-16 12:48:08.562 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:48:08.562 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:48:38.574 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:48:38.576 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 12:49:08.583 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 12:49:08.583 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:06:47.441 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m38s859ms).
2025-07-16 13:06:47.443 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:06:47.443 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:07:17.436 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:07:17.437 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:24:55.322 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m37s886ms).
2025-07-16 13:24:55.324 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:24:55.324 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:25:25.317 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:25:25.319 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:36:46.834 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=11m21s517ms).
2025-07-16 13:36:46.835 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:36:46.835 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:52:53.512 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=16m6s678ms).
2025-07-16 13:52:53.513 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:52:53.514 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 13:53:23.503 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 13:53:23.503 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:02:00.071 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=8m36s568ms).
2025-07-16 14:02:00.074 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:02:00.074 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:02:30.078 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:02:30.079 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:03:00.083 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:03:00.084 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:03:30.087 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:03:30.088 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:04:00.094 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:04:00.095 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:04:30.098 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:04:30.098 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:05:00.102 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:05:00.102 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:05:30.105 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:05:30.106 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:06:00.112 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:06:00.113 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:06:30.118 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:06:30.118 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:07:00.124 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:07:00.124 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:07:30.130 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:07:30.130 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:08:00.136 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:08:00.137 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:08:30.162 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:08:30.162 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:09:00.175 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:09:00.176 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:09:30.180 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:09:30.181 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:10:00.192 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:10:00.193 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:10:30.199 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:10:30.200 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:11:00.202 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:11:00.203 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:11:30.209 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:11:30.209 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:12:00.214 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:12:00.215 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:12:30.217 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:12:30.217 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:13:00.223 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:13:00.224 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:13:30.229 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:13:30.229 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:14:00.235 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:14:00.236 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:14:30.242 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:14:30.242 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:15:00.248 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:15:00.249 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:15:30.255 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:15:30.256 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:15:45.176 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@111ad1ee: (connection has passed maxLifetime)
2025-07-16 14:15:45.184 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@105bbb95
2025-07-16 14:15:45.462 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@17efe12b: (connection has passed maxLifetime)
2025-07-16 14:15:45.466 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5166b1ae
2025-07-16 14:15:46.404 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@59dade9e: (connection has passed maxLifetime)
2025-07-16 14:15:46.409 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@607de172
2025-07-16 14:15:48.707 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@331bdb9d: (connection has passed maxLifetime)
2025-07-16 14:15:48.711 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31494ee1
2025-07-16 14:15:51.408 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@346a8861: (connection has passed maxLifetime)
2025-07-16 14:15:51.412 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@28130e94
2025-07-16 14:15:53.390 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@69f0164e: (connection has passed maxLifetime)
2025-07-16 14:15:53.393 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35079311
2025-07-16 14:15:53.857 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@8869a13: (connection has passed maxLifetime)
2025-07-16 14:15:53.860 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@72a68862
2025-07-16 14:16:00.262 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:16:00.262 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:16:00.787 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@bd7917d: (connection has passed maxLifetime)
2025-07-16 14:16:00.791 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@16611c4e
2025-07-16 14:16:03.371 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2e59ebb6: (connection has passed maxLifetime)
2025-07-16 14:16:03.376 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@575c1675
2025-07-16 14:16:11.129 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5c41d037: (connection has passed maxLifetime)
2025-07-16 14:16:11.132 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b1bf4b
2025-07-16 14:16:11.310 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7e3366ee: (connection has passed maxLifetime)
2025-07-16 14:16:11.315 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@408cb570
2025-07-16 14:16:11.948 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1aa6353a: (connection has passed maxLifetime)
2025-07-16 14:16:11.951 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@71069658
2025-07-16 14:16:15.030 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@335d9005: (connection has passed maxLifetime)
2025-07-16 14:16:15.032 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@20a34a3f
2025-07-16 14:16:17.142 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@39f521d1: (connection has passed maxLifetime)
2025-07-16 14:16:17.146 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@11cd982b
2025-07-16 14:16:18.263 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5d9463e5: (connection has passed maxLifetime)
2025-07-16 14:16:18.267 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@24ed163d
2025-07-16 14:16:19.312 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@44454d30: (connection has passed maxLifetime)
2025-07-16 14:16:19.317 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6e9215f5
2025-07-16 14:16:21.931 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@2606dd9a: (connection has passed maxLifetime)
2025-07-16 14:16:21.933 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@16b84a30
2025-07-16 14:16:24.552 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@78c82bea: (connection has passed maxLifetime)
2025-07-16 14:16:24.556 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@654e3877
2025-07-16 14:16:28.065 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@56918621: (connection has passed maxLifetime)
2025-07-16 14:16:28.067 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2c5bfedd
2025-07-16 14:16:28.861 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@22843e32: (connection has passed maxLifetime)
2025-07-16 14:16:28.864 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4e63616b
2025-07-16 14:16:30.268 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:16:30.269 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:17:00.275 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:17:00.276 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:17:30.279 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:17:30.280 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:18:00.285 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:18:00.285 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:18:05.116 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 14:18:05.116 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 14:18:05.116 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 14:18:05.116 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 14:18:06.152 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@3a66e67e
2025-07-16 14:18:06.154 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@3a66e67e
2025-07-16 14:18:08.343 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 14:18:08.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 14:18:08.446 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:18:08.449 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:18:08.450 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:18:08.451 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:18:08.452 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:18:08.453 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:18:08.454 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:18:08.455 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:18:08.455 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 14:18:08.516 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 14:18:08.516 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 14:18:08.589 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 14:18:08.589 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 14:18:08.590 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 14:18:08.590 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 14:18:08.590 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 14:18:08.590 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 14:18:08.591 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 14:18:08.605 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 14:18:08.605 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 14:18:08.605 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 14:18:08.605 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 14:18:08.605 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 14:18:08.610 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 14:18:08.610 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 14:18:08.614 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 14:18:08.615 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:18:08.618 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:18:08.618 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:18:08.621 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 14:18:08.623 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@28060e01
2025-07-16 14:18:08.627 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.012s)
2025-07-16 14:18:08.628 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 14:18:08.631 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:18:08.632 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 14:18:08.633 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 14:18:08.637 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 26 of 80M
2025-07-16 14:18:08.637 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 14:18:08.637 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 14:18:08.637 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@693b043a
2025-07-16 14:18:08.652 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5898a901
2025-07-16 14:18:08.666 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@914fb75
2025-07-16 14:18:08.669 [main] INFO  Application - Application started in 0.335 seconds.
2025-07-16 14:18:08.669 [main] INFO  Application - Application started: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:18:08.673 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 14:18:08.682 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@62517b9d
2025-07-16 14:18:08.682 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 14:18:08.682 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 14:18:08.683 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 14:18:08.683 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 14:18:08.683 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 14:18:08.683 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 14:18:08.683 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 14:18:08.684 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 14:18:08.684 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 14:18:08.684 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 14:18:08.684 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 14:18:08.691 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 14:18:08.691 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 14:18:08.691 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 14:18:08.691 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 14:18:08.692 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 14:18:08.692 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 14:18:08.692 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 14:18:08.692 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 14:18:08.694 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 14:18:08.694 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 14:18:08.694 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 14:18:08.694 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 14:18:08.697 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@d5a86d8
2025-07-16 14:18:08.708 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 14:18:08.711 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 14:18:08.713 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 14:18:08.713 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 14:18:08.713 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7b134c08
2025-07-16 14:18:08.714 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 14:18:08.715 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 14:18:08.717 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 14:18:08.727 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 12951 (auto-detected)
2025-07-16 14:18:08.729 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 14:18:08.729 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 14:18:08.730 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@591260bf
2025-07-16 14:18:08.733 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 14:18:08.733 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 14:18:08.734 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 14:18:08.738 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 14:18:08.738 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 14:18:08.747 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@105def68
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 14:18:08.749 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 14:18:08.750 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 14:18:08.750 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 14:18:08.750 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 14:18:08.750 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 14:18:08.755 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 14:18:08.755 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 14:18:08.755 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 14:18:08.762 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@604cf351
2025-07-16 14:18:08.763 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 14:18:08.777 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@12257053
2025-07-16 14:18:08.792 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@348dc8bb
2025-07-16 14:18:08.807 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@744675d
2025-07-16 14:18:08.822 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@34958337
2025-07-16 14:18:08.836 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1d79a017
2025-07-16 14:18:08.851 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1b4cf956
2025-07-16 14:18:08.865 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6170cf4d
2025-07-16 14:18:08.879 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5ce2f798
2025-07-16 14:18:08.894 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@47e9bfe7
2025-07-16 14:18:08.906 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:18:13.405 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 14:18:13.405 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 14:18:13.405 [eventLoopGroupProxy-3-2] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@2d6f2159
2025-07-16 14:18:13.425 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 14:18:13.426 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 14:18:13.426 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 14:18:13.426 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 14:18:13.426 [eventLoopGroupProxy-3-2] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 14:18:13.486 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 14:18:13.519 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:18:13.519 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:18:13.521 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:18:13.558 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:18:13.558 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:18:33.292 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:18:33.292 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:18:33.293 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:18:33.294 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:18:33.295 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:18:38.627 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:18:38.628 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:18:53.667 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE requirements.assignee_id = 2
2025-07-16 14:18:53.669 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.assignee_id = 2 ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:18:53.671 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&assigneeId=2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:18:55.998 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE requirements.assignee_id = 1
2025-07-16 14:18:56.000 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.assignee_id = 1 ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:18:56.001 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&assigneeId=1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:01.091 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE (requirements.status = 'DRAFT') AND (requirements.assignee_id = 1)
2025-07-16 14:19:01.092 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE (requirements.status = 'DRAFT') AND (requirements.assignee_id = 1) ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:01.094 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&status=DRAFT&assigneeId=1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:03.368 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE (requirements.status = 'REVIEW') AND (requirements.assignee_id = 1)
2025-07-16 14:19:03.370 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE (requirements.status = 'REVIEW') AND (requirements.assignee_id = 1) ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:03.372 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&status=REVIEW&assigneeId=1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:04.164 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:19:04.165 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:04.169 [eventLoopGroupProxy-4-9] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:08.634 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:19:08.634 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:19:31.995 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:19:31.995 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:19:31.997 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:31.997 [eventLoopGroupProxy-4-10] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:31.998 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:35.613 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE requirements.assignee_id = 1
2025-07-16 14:19:35.614 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.assignee_id = 1 ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:35.616 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&assigneeId=1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:37.998 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE requirements.assignee_id = 2
2025-07-16 14:19:38.000 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.assignee_id = 2 ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:38.002 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&assigneeId=2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:38.637 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:19:38.637 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:19:40.485 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements WHERE (requirements.status = 'DRAFT') AND (requirements.assignee_id = 2)
2025-07-16 14:19:40.486 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE (requirements.status = 'DRAFT') AND (requirements.assignee_id = 2) ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:40.487 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20&status=DRAFT&assigneeId=2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:19:41.175 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:19:41.176 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:19:41.179 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:20:08.642 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:20:08.642 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:20:32.533 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 1
2025-07-16 14:20:32.537 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:20:32.540 [eventLoopGroupProxy-4-6] INFO  Application - 400 Bad Request: PUT /api/requirements/1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:20:38.644 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:20:38.645 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:21:08.646 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:21:08.646 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:21:34.159 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 1
2025-07-16 14:21:34.162 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:21:34.164 [eventLoopGroupProxy-4-7] INFO  Application - 400 Bad Request: PUT /api/requirements/1 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:21:38.652 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:21:38.653 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:22:08.654 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:22:08.655 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:22:38.658 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:22:38.660 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:23:08.665 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:23:08.667 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:23:38.670 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:23:38.671 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:24:08.594 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:24:08.595 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:24:38.601 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:24:38.602 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:25:08.607 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:25:08.608 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:25:38.613 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:25:38.613 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:26:08.619 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:26:08.619 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:26:38.625 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:26:38.626 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:27:08.631 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:27:08.632 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:27:23.976 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 1
2025-07-16 14:27:23.979 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:27:24.007 [DefaultDispatcher-worker-2] DEBUG Exposed - INSERT INTO requirements (acceptance_criteria, actual_delivery_date, assignee_id, business_description, business_goal, created_at, creator_id, estimated_value, expected_delivery_date, priority_importance, priority_urgency, status, target_users, title, updated_at) VALUES (NULL, NULL, 2, '应用描述：全球畅享，视听无界——[应用名称] 国际电视版​\n\n​​[应用名称]​​ 是一款专为全球用户打造的高品质电视端应用，提供流畅的直播（Live）​与海量点播（VOD）​内容，让您足不出户即可尽享世界精彩。无论您身处何地，只需打开电视，即可体验高清画质、多语言支持与个性化推荐，打造专属您的家庭娱乐中心。\n\n​核心亮点​\n✅ ​全球直播，即时畅看——覆盖新闻、体育、娱乐、音乐等热门频道，24小时不间断播放，与世界同步。\n✅ ​海量点播，随心观看——电影、剧集、纪录片、儿童内容一应俱全，支持多语言字幕与配音，满足全家需求。\n✅ ​超清体验，流畅稳定——自适应码率技术，确保不同网络环境下高清播放，支持4K/HDR，沉浸感十足。\n✅ ​个性化推荐——智能算法学习您的偏好，精准推荐内容，省去搜索烦恼。\n✅ ​多语言支持——界面与内容适配英语、西班牙语、法语、阿拉伯语等主流语言，轻松切换无障碍。\n✅ ​跨设备同步——支持账号多端登录，随时随地续播，观影不间断。\n\n​适用场景​\n🌍 ​海外华人——家乡频道、华语影视一键直达，缓解乡愁。\n🏠 ​国际家庭——多语言内容满足不同成员需求，老少皆宜。\n⚽ ​体育爱好者——足球、NBA、F1等赛事直播不错过。\n🎬 ​影迷剧控——好莱坞大片、热门剧集抢先看。\n\n​​[应用名称]​​ 现已登陆智能电视、机顶盒、投影仪等大屏设备，快来开启您的全球视听之旅！\n\n​立即下载，坐拥世界！​​', NULL, '2025-07-16 14:27:23.983669', 1, NULL, NULL, 'MEDIUM', 'MEDIUM', 'DRAFT', '所有TV用户', '国际版', '2025-07-16 14:27:23.983702')
2025-07-16 14:27:24.010 [eventLoopGroupProxy-4-8] INFO  c.b.services.RequirementService - 用户 1 创建了需求: 国际版
2025-07-16 14:27:24.012 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: POST /api/requirements - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:27:24.022 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:27:24.024 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:27:24.026 [eventLoopGroupProxy-4-9] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:27:38.638 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:27:38.638 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:27:51.803 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:27:51.805 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:27:51.807 [eventLoopGroupProxy-4-10] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:28:08.640 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:28:08.641 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:28:23.771 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:28:23.773 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:28:23.775 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:28:38.646 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:28:38.647 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:29:08.652 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:29:08.653 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:29:38.658 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:29:38.660 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:30:08.665 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:30:08.667 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:30:18.851 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:30:18.851 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:30:18.852 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:30:18.853 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:30:18.855 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:30:31.996 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:30:31.998 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:30:32.000 [eventLoopGroupProxy-4-3] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:30:38.672 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:30:38.672 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:31:08.675 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:31:08.675 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:31:38.681 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:31:38.681 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:31:52.275 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:31:52.275 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:31:52.276 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:31:52.276 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:31:52.278 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:32:00.460 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:32:00.462 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:32:00.464 [eventLoopGroupProxy-4-7] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:32:08.687 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:32:08.687 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:32:38.691 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:32:38.691 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:33:08.600 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:33:08.600 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:33:08.601 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:33:08.601 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:33:08.603 [eventLoopGroupProxy-4-9] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:33:08.692 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:33:08.692 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:33:10.983 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:33:10.983 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:33:10.984 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:33:10.985 [eventLoopGroupProxy-4-10] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:33:10.986 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:33:18.608 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:33:18.611 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:33:18.612 [eventLoopGroupProxy-4-2] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:33:38.698 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:33:38.699 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:34:08.702 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:34:08.702 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:34:38.706 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:34:38.706 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:35:08.711 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:35:08.711 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:35:38.714 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:35:38.714 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:36:08.718 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:36:08.719 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:36:38.723 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:36:38.724 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:36:55.956 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:36:55.956 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:36:55.958 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:36:55.958 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:36:55.960 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:37:08.727 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:37:08.728 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:37:38.734 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:37:38.735 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:38:08.740 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:38:08.741 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:38:38.746 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:38:38.748 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:39:08.752 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:39:08.753 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:39:38.759 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:39:38.759 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:40:08.769 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:40:08.769 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:40:38.697 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:40:38.697 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:40:38.698 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:40:38.698 [eventLoopGroupProxy-4-5] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:40:38.701 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:40:38.800 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:40:38.800 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:40:49.045 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:40:49.046 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:40:49.047 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:40:49.049 [eventLoopGroupProxy-4-8] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:40:49.052 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:40:56.870 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:40:56.872 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:40:56.873 [eventLoopGroupProxy-4-9] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:41:08.809 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:41:08.809 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:41:38.674 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:41:38.674 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:41:38.675 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:41:38.676 [eventLoopGroupProxy-4-10] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:41:38.678 [eventLoopGroupProxy-4-1] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:41:38.811 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:41:38.812 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:41:46.038 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:41:46.040 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:41:46.042 [eventLoopGroupProxy-4-2] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:42:08.818 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:42:08.818 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:42:38.822 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:42:38.823 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:43:08.828 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:43:08.829 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:43:38.835 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:43:38.835 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:43:47.011 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:43:47.011 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:43:47.013 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:43:47.013 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:43:47.015 [eventLoopGroupProxy-4-4] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:44:08.841 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:44:08.841 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:44:38.844 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:44:38.845 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:45:08.846 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:45:08.848 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:45:19.695 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:45:19.698 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:45:19.700 [eventLoopGroupProxy-4-5] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:45:38.853 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:45:38.854 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:46:08.859 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:46:08.860 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:46:38.865 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:46:38.867 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:47:08.870 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:47:08.872 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:47:24.221 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185: (connection has passed maxLifetime)
2025-07-16 14:47:24.228 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@544d438f
2025-07-16 14:47:25.893 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@d5a86d8: (connection has passed maxLifetime)
2025-07-16 14:47:25.898 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@157049b4
2025-07-16 14:47:26.728 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1b4cf956: (connection has passed maxLifetime)
2025-07-16 14:47:26.733 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@242f5b9e
2025-07-16 14:47:27.323 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@28060e01: (connection has passed maxLifetime)
2025-07-16 14:47:27.328 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@755fb6fb
2025-07-16 14:47:27.356 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5ce2f798: (connection has passed maxLifetime)
2025-07-16 14:47:27.360 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@30ba9ce7
2025-07-16 14:47:28.764 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@62517b9d: (connection has passed maxLifetime)
2025-07-16 14:47:28.769 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@71112f0d
2025-07-16 14:47:32.127 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@105def68: (connection has passed maxLifetime)
2025-07-16 14:47:32.131 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4527da86
2025-07-16 14:47:32.885 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@591260bf: (connection has passed maxLifetime)
2025-07-16 14:47:32.889 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4986b87a
2025-07-16 14:47:38.795 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@914fb75: (connection has passed maxLifetime)
2025-07-16 14:47:38.799 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7973f6e3
2025-07-16 14:47:38.878 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:47:38.878 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:47:39.202 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@693b043a: (connection has passed maxLifetime)
2025-07-16 14:47:39.206 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@384efcd8
2025-07-16 14:47:41.707 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@744675d: (connection has passed maxLifetime)
2025-07-16 14:47:41.712 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@93ce564
2025-07-16 14:47:43.512 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@604cf351: (connection has passed maxLifetime)
2025-07-16 14:47:43.518 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@58610397
2025-07-16 14:47:44.137 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@7b134c08: (connection has passed maxLifetime)
2025-07-16 14:47:44.142 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@2f8b5a1c
2025-07-16 14:47:47.450 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@348dc8bb: (connection has passed maxLifetime)
2025-07-16 14:47:47.453 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5c2fbf4f
2025-07-16 14:47:50.097 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@12257053: (connection has passed maxLifetime)
2025-07-16 14:47:50.102 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7c222a56
2025-07-16 14:47:51.049 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@6170cf4d: (connection has passed maxLifetime)
2025-07-16 14:47:51.054 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@428c1e36
2025-07-16 14:47:51.844 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@47e9bfe7: (connection has passed maxLifetime)
2025-07-16 14:47:51.848 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4024619f
2025-07-16 14:47:52.288 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@34958337: (connection has passed maxLifetime)
2025-07-16 14:47:52.292 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@41a65025
2025-07-16 14:47:53.095 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@1d79a017: (connection has passed maxLifetime)
2025-07-16 14:47:53.100 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@43c21d5c
2025-07-16 14:48:04.492 [HikariPool-1 connection closer] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection com.mysql.cj.jdbc.ConnectionImpl@5898a901: (connection has passed maxLifetime)
2025-07-16 14:48:04.497 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@765e033b
2025-07-16 14:48:08.882 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:48:08.883 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:48:38.888 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:48:38.890 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 6 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 6 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 6 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 8 thread-local buffer(s) from thread: eventLoopGroupProxy-3-6
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 6 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 14:49:01.292 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 6 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 14:49:01.294 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:49:01.295 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:49:03.059 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 14:49:03.169 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 14:49:03.172 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:49:03.174 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:49:03.175 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:49:03.176 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:49:03.177 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:49:03.178 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:49:03.178 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 14:49:03.234 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185
2025-07-16 14:49:03.234 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 14:49:03.315 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 14:49:03.315 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 14:49:03.316 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 14:49:03.316 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 14:49:03.316 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 14:49:03.316 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 14:49:03.317 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 14:49:03.332 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 14:49:03.332 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 14:49:03.332 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 14:49:03.333 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 14:49:03.333 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 14:49:03.338 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 14:49:03.339 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 14:49:03.339 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 14:49:03.342 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@68ff5bf4
2025-07-16 14:49:03.343 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 14:49:03.344 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:49:03.347 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:49:03.347 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:49:03.357 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@29c251cf
2025-07-16 14:49:03.358 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.014s)
2025-07-16 14:49:03.361 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 14:49:03.370 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:49:03.372 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@18a1dd58
2025-07-16 14:49:03.373 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 14:49:03.377 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 14:49:03.388 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 26 of 80M
2025-07-16 14:49:03.388 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 14:49:03.388 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 14:49:03.388 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@49edae80
2025-07-16 14:49:03.407 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4d896e28
2025-07-16 14:49:03.423 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4da313cc
2025-07-16 14:49:03.440 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@31eb1d80
2025-07-16 14:49:03.446 [main] INFO  Application - Application started in 0.399 seconds.
2025-07-16 14:49:03.447 [main] INFO  Application - Application started: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:49:03.452 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 14:49:03.457 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7ccbb156
2025-07-16 14:49:03.464 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 14:49:03.465 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 14:49:03.466 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 14:49:03.466 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 14:49:03.466 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 14:49:03.466 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 14:49:03.466 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 14:49:03.467 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 14:49:03.468 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @3e92efc3
2025-07-16 14:49:03.468 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 14:49:03.468 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 14:49:03.473 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@cc2abee
2025-07-16 14:49:03.478 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 14:49:03.478 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 14:49:03.478 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 14:49:03.478 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 14:49:03.479 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 14:49:03.479 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 14:49:03.479 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 14:49:03.479 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 14:49:03.482 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 14:49:03.482 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 14:49:03.482 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 14:49:03.482 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 14:49:03.489 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1be18abe
2025-07-16 14:49:03.495 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 14:49:03.497 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 14:49:03.498 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 14:49:03.498 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 14:49:03.500 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 14:49:03.500 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 14:49:03.502 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 14:49:03.504 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@22703c8
2025-07-16 14:49:03.509 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 14040 (auto-detected)
2025-07-16 14:49:03.511 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 14:49:03.511 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 14:49:03.513 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 14:49:03.513 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 14:49:03.513 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 14:49:03.516 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 14:49:03.516 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 14:49:03.519 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@215c222b
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 14:49:03.524 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 14:49:03.527 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 14:49:03.527 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 14:49:03.527 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 14:49:03.535 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1ac9c115
2025-07-16 14:49:03.535 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 14:49:03.551 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b8f5097
2025-07-16 14:49:03.566 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@35aeab3b
2025-07-16 14:49:03.582 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@49fd4598
2025-07-16 14:49:03.597 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@27834a4
2025-07-16 14:49:03.615 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@44e7918a
2025-07-16 14:49:03.630 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@384cc569
2025-07-16 14:49:03.643 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:49:13.068 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 14:49:13.069 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 14:49:13.070 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@433ffe43
2025-07-16 14:49:13.090 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 14:49:13.090 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 14:49:13.090 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 14:49:13.090 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 14:49:13.090 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 14:49:13.170 [DefaultDispatcher-worker-2] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@4bff2185 due to dirty commit state on close().
2025-07-16 14:49:13.199 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:49:13.207 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:49:13.214 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:49:33.345 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:49:33.346 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:49:57.107 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 14:49:58.146 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:49:58.148 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@4a7fd0c9
2025-07-16 14:49:59.333 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 14:49:59.436 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 14:49:59.439 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:49:59.441 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:49:59.442 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:49:59.443 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:49:59.444 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:49:59.445 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:49:59.446 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 14:49:59.561 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3af356f
2025-07-16 14:49:59.562 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 14:49:59.667 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 14:49:59.672 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@15180099
2025-07-16 14:49:59.689 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@618029db
2025-07-16 14:49:59.702 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 14:49:59.702 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:49:59.702 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 14:49:59.703 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 14:49:59.703 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 14:49:59.703 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 14:49:59.703 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 14:49:59.703 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:49:59.703 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 14:49:59.703 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 14:49:59.703 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 14:49:59.704 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 14:49:59.704 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 14:49:59.705 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@345caa22
2025-07-16 14:49:59.705 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 14:49:59.720 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@347b3f85
2025-07-16 14:49:59.722 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 14:49:59.722 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 14:49:59.722 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 14:49:59.723 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 14:49:59.723 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 14:49:59.730 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 14:49:59.731 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 14:49:59.735 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 14:49:59.735 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@656086c5
2025-07-16 14:49:59.736 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:49:59.741 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:49:59.741 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:49:59.751 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3fdeeb6f
2025-07-16 14:49:59.756 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.020s)
2025-07-16 14:49:59.758 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 14:49:59.763 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:49:59.766 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 14:49:59.766 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@550c4556
2025-07-16 14:49:59.769 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 14:49:59.777 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 36 of 80M
2025-07-16 14:49:59.777 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 14:49:59.778 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 14:49:59.785 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6d13cd10
2025-07-16 14:49:59.800 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@d80adb0
2025-07-16 14:49:59.816 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@19eb540e
2025-07-16 14:49:59.834 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5be6e4ef
2025-07-16 14:49:59.854 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23e56647
2025-07-16 14:49:59.873 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1c7729fb
2025-07-16 14:49:59.892 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@615ad848
2025-07-16 14:49:59.908 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@6b151785
2025-07-16 14:49:59.920 [main] INFO  Application - Application started in 0.602 seconds.
2025-07-16 14:49:59.921 [main] INFO  Application - Application started: io.ktor.server.application.Application@3c3820bb
2025-07-16 14:49:59.923 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@23ac8204
2025-07-16 14:49:59.924 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 14:49:59.932 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 14:49:59.933 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 14:49:59.934 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @7714e963
2025-07-16 14:49:59.934 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 14:49:59.934 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 14:49:59.938 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@448c38c3
2025-07-16 14:49:59.944 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 14:49:59.944 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 14:49:59.944 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 14:49:59.944 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 14:49:59.945 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 14:49:59.945 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 14:49:59.946 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 14:49:59.946 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 14:49:59.949 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 14:49:59.949 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 14:49:59.949 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 14:49:59.949 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 14:49:59.953 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@318e52c6
2025-07-16 14:49:59.965 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 14:49:59.967 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@40e29e5e
2025-07-16 14:49:59.972 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 14:49:59.974 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 14:49:59.975 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 14:49:59.978 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:49:59.980 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 14:49:59.980 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 14:49:59.986 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 14:50:00.001 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 14069 (auto-detected)
2025-07-16 14:50:00.003 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 14:50:00.003 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 14:50:00.006 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 14:50:00.006 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 14:50:00.007 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 14:50:00.026 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 14:50:00.026 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 14:50:00.039 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 14:50:00.040 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 14:50:00.046 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 14:50:00.046 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 14:50:00.046 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 14:50:00.062 [DefaultDispatcher-worker-2] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 14:50:02.452 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 14:50:02.452 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 14:50:02.452 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@2ed4d37e
2025-07-16 14:50:02.476 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 14:50:02.477 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 14:50:02.477 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 14:50:02.477 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 14:50:02.477 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 14:50:02.550 [DefaultDispatcher-worker-2] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@3af356f due to dirty commit state on close().
2025-07-16 14:50:02.584 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:50:02.589 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:50:02.597 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:50:29.674 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:50:29.675 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:50:59.681 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:50:59.681 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:51:13.079 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 14:51:14.117 [main] INFO  Application - Application stopping: io.ktor.server.application.Application@3c3820bb
2025-07-16 14:51:14.118 [main] INFO  Application - Application stopped: io.ktor.server.application.Application@3c3820bb
2025-07-16 14:51:15.159 [main] INFO  Application - Autoreload is disabled because the development mode is off.
2025-07-16 14:51:15.248 [main] DEBUG com.zaxxer.hikari.HikariConfig - Driver class com.mysql.cj.jdbc.Driver found in Thread context class loader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6
2025-07-16 14:51:15.250 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:51:15.252 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:51:15.253 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:51:15.254 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:51:15.255 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:51:15.255 [main] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-07-16 14:51:15.255 [main] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension.............false
2025-07-16 14:51:15.255 [main] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit......................false
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - catalog.........................none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql...............none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery.............none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout...............30000
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSource......................none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName.............none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI..................none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties............{password=<masked>}
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................."com.mysql.cj.jdbc.Driver"
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName......none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties...........{}
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry.............none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout.....................***********-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout.......1
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries..........false
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl.........................************************************?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=Asia/Shanghai
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime...................0
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold..........0
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime.....................1800000
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize.................20
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry..................none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory...........none
2025-07-16 14:51:15.256 [main] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle.....................20
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - password........................<masked>
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - poolName........................"HikariPool-1"
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - readOnly........................false
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans..................false
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor...............none
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - schema..........................none
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory...................internal
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation............"TRANSACTION_REPEATABLE_READ"
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - username........................"root"
2025-07-16 14:51:15.257 [main] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout...............5000
2025-07-16 14:51:15.257 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-16 14:51:15.328 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3af356f
2025-07-16 14:51:15.329 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-16 14:51:15.430 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-07-16 14:51:15.436 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7173382a
2025-07-16 14:51:15.452 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@50f8abe
2025-07-16 14:51:15.466 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-07-16 14:51:15.467 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - Flyway OSS Edition 10.0.0 by Redgate
2025-07-16 14:51:15.467 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - 
2025-07-16 14:51:15.467 [main] INFO  o.f.c.i.s.classpath.ClassPathScanner - See release notes here: https://rd.gt/416ObMi
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader jdk.internal.loader.ClassLoaders$AppClassLoader@1dbd16a6 ...
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/
2025-07-16 14:51:15.467 [main] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /Users/<USER>/VSSpace/beefcake/backend/build/resources/main/db/migration (db/migration)
2025-07-16 14:51:15.468 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__Create_initial_tables.sql
2025-07-16 14:51:15.468 [main] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-07-16 14:51:15.468 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@48626907
2025-07-16 14:51:15.469 [main] DEBUG o.f.c.i.r.ResourceNameValidator - Validating V1__Create_initial_tables.sql
2025-07-16 14:51:15.484 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5010ffd8
2025-07-16 14:51:15.488 [main] INFO  org.flywaydb.core.FlywayExecutor - Database: ************************************ (MySQL 8.4)
2025-07-16 14:51:15.488 [main] DEBUG org.flywaydb.core.FlywayExecutor - Driver: MySQL Connector/J mysql-connector-j-8.0.33 (Revision: 7d6b0800528b6b25c68b52dc10d6c1c8429c100c)
2025-07-16 14:51:15.488 [main] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: false
2025-07-16 14:51:15.489 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-07-16 14:51:15.489 [main] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-07-16 14:51:15.497 [main] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: MySQL 8.4 is newer than this version of Flyway and support has not been tested. The latest supported version of MySQL is 8.1.
2025-07-16 14:51:15.497 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4bffc6af
2025-07-16 14:51:15.497 [main] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-07-16 14:51:15.501 [main] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-07-16 14:51:15.503 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:51:15.506 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:51:15.506 [main] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__Create_initial_tables.sql (filename: V1__Create_initial_tables.sql)
2025-07-16 14:51:15.512 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@4012b068
2025-07-16 14:51:15.519 [main] INFO  o.f.core.internal.command.DbValidate - Successfully validated 1 migration (execution time 00:00.017s)
2025-07-16 14:51:15.521 [main] DEBUG o.f.core.internal.command.DbSchemas - Skipping creation of existing schema: `beefcake`
2025-07-16 14:51:15.526 [main] DEBUG o.f.c.a.c.ClassicConfiguration - CherryPickConfigurationExtension not found
2025-07-16 14:51:15.527 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@65a8a42a
2025-07-16 14:51:15.529 [main] INFO  o.f.core.internal.command.DbMigrate - Current version of schema `beefcake`: 1
2025-07-16 14:51:15.531 [main] INFO  o.f.core.internal.command.DbMigrate - Schema `beefcake` is up to date. No migration necessary.
2025-07-16 14:51:15.536 [main] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 36 of 80M
2025-07-16 14:51:15.536 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库迁移完成
2025-07-16 14:51:15.536 [main] INFO  c.beefcake.database.DatabaseFactory - 数据库初始化完成
2025-07-16 14:51:15.543 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@407ec736
2025-07-16 14:51:15.558 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@5796c257
2025-07-16 14:51:15.573 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@259ee792
2025-07-16 14:51:15.582 [main] INFO  Application - Application started in 0.436 seconds.
2025-07-16 14:51:15.582 [main] INFO  Application - Application started: io.ktor.server.application.Application@3c3820bb
2025-07-16 14:51:15.585 [main] DEBUG i.n.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-07-16 14:51:15.589 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@77e7774c
2025-07-16 14:51:15.594 [main] DEBUG i.n.util.internal.PlatformDependent0 - -Dio.netty.noUnsafe: false
2025-07-16 14:51:15.594 [main] DEBUG i.n.util.internal.PlatformDependent0 - Java version: 17
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.theUnsafe: available
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.copyMemory: available
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - sun.misc.Unsafe.storeFence: available
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Buffer.address: available
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - direct buffer constructor: unavailable: Reflective setAccessible(true) disabled
2025-07-16 14:51:15.595 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.Bits.unaligned: available, true
2025-07-16 14:51:15.596 [main] DEBUG i.n.util.internal.PlatformDependent0 - jdk.internal.misc.Unsafe.allocateUninitializedArray(int): unavailable: class io.netty.util.internal.PlatformDependent0$7 cannot access class jdk.internal.misc.Unsafe (in module java.base) because module java.base does not export jdk.internal.misc to unnamed module @7714e963
2025-07-16 14:51:15.596 [main] DEBUG i.n.util.internal.PlatformDependent0 - java.nio.DirectByteBuffer.<init>(long, int): unavailable
2025-07-16 14:51:15.596 [main] DEBUG i.n.util.internal.PlatformDependent - sun.misc.Unsafe: available
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - maxDirectMemory: 6442450944 bytes (maybe)
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.tmpdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (java.io.tmpdir)
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.bitMode: 64 (sun.arch.data.model)
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - Platform: MacOS
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.maxDirectMemory: -1 bytes
2025-07-16 14:51:15.602 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.uninitializedArrayAllocationThreshold: -1
2025-07-16 14:51:15.603 [main] DEBUG io.netty.util.internal.CleanerJava9 - java.nio.ByteBuffer.cleaner(): available
2025-07-16 14:51:15.603 [main] DEBUG i.n.util.internal.PlatformDependent - -Dio.netty.noPreferDirect: false
2025-07-16 14:51:15.604 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@8c12c1f
2025-07-16 14:51:15.606 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.workdir: /var/folders/ck/m5t3pltj2pjgr31_btggvm900000gn/T (io.netty.tmpdir)
2025-07-16 14:51:15.606 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.deleteLibAfterLoading: true
2025-07-16 14:51:15.606 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.tryPatchShadedId: true
2025-07-16 14:51:15.606 [main] DEBUG i.n.u.internal.NativeLibraryLoader - -Dio.netty.native.detectNativeLibraryDuplicates: true
2025-07-16 14:51:15.618 [main] DEBUG i.n.c.MultithreadEventLoopGroup - -Dio.netty.eventLoopThreads: 20
2025-07-16 14:51:15.620 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@236e5ff0
2025-07-16 14:51:15.622 [main] DEBUG i.n.u.concurrent.GlobalEventExecutor - -Dio.netty.globalEventExecutor.quietPeriodSeconds: 1
2025-07-16 14:51:15.623 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.initialSize: 1024
2025-07-16 14:51:15.623 [main] DEBUG i.n.u.i.InternalThreadLocalMap - -Dio.netty.threadLocalMap.stringBuilder.maxSize: 4096
2025-07-16 14:51:15.625 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.noKeySetOptimization: false
2025-07-16 14:51:15.625 [main] DEBUG io.netty.channel.nio.NioEventLoop - -Dio.netty.selectorAutoRebuildThreshold: 512
2025-07-16 14:51:15.628 [main] DEBUG i.n.util.internal.PlatformDependent - org.jctools-core.MpscChunkedArrayQueue: available
2025-07-16 14:51:15.636 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.processId: 14115 (auto-detected)
2025-07-16 14:51:15.637 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv4Stack: false
2025-07-16 14:51:15.637 [main] DEBUG io.netty.util.NetUtil - -Djava.net.preferIPv6Addresses: false
2025-07-16 14:51:15.637 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@3c6fb915
2025-07-16 14:51:15.639 [main] DEBUG i.netty.util.NetUtilInitializations - Loopback interface: lo0 (lo0, 0:0:0:0:0:0:0:1%lo0)
2025-07-16 14:51:15.640 [main] DEBUG io.netty.util.NetUtil - Failed to get SOMAXCONN from sysctl and file /proc/sys/net/core/somaxconn. Default: 128
2025-07-16 14:51:15.640 [main] DEBUG io.netty.channel.DefaultChannelId - -Dio.netty.machineId: ba:32:c6:ff:fe:60:27:2c (auto-detected)
2025-07-16 14:51:15.646 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.level: simple
2025-07-16 14:51:15.646 [main] DEBUG io.netty.util.ResourceLeakDetector - -Dio.netty.leakDetection.targetRecords: 4
2025-07-16 14:51:15.652 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@724d558f
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numHeapArenas: 20
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.numDirectArenas: 20
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.pageSize: 8192
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxOrder: 9
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.chunkSize: 4194304
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.smallCacheSize: 256
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.normalCacheSize: 64
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedBufferCapacity: 32768
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimInterval: 8192
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.cacheTrimIntervalMillis: 0
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.useCacheForAllThreads: false
2025-07-16 14:51:15.657 [main] DEBUG i.n.buffer.PooledByteBufAllocator - -Dio.netty.allocator.maxCachedByteBuffersPerChunk: 1023
2025-07-16 14:51:15.665 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.allocator.type: pooled
2025-07-16 14:51:15.665 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.threadLocalDirectBufferSize: 0
2025-07-16 14:51:15.665 [main] DEBUG io.netty.buffer.ByteBufUtil - -Dio.netty.maxThreadLocalCharBufferSize: 16384
2025-07-16 14:51:15.668 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@462a270d
2025-07-16 14:51:15.678 [DefaultDispatcher-worker-1] INFO  Application - Responding at http://0.0.0.0:8080
2025-07-16 14:51:15.686 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7f08101f
2025-07-16 14:51:15.702 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@499ae7e3
2025-07-16 14:51:15.719 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@789c178a
2025-07-16 14:51:15.732 [HikariPool-1 connection adder] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:51:22.124 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkAccessible: true
2025-07-16 14:51:22.124 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.AbstractByteBuf - -Dio.netty.buffer.checkBounds: true
2025-07-16 14:51:22.125 [eventLoopGroupProxy-3-1] DEBUG i.n.util.ResourceLeakDetectorFactory - Loaded default ResourceLeakDetector: io.netty.util.ResourceLeakDetector@6987f2e7
2025-07-16 14:51:22.149 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.maxCapacityPerThread: 4096
2025-07-16 14:51:22.149 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.ratio: 8
2025-07-16 14:51:22.149 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.chunkSize: 32
2025-07-16 14:51:22.149 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.blocking: false
2025-07-16 14:51:22.149 [eventLoopGroupProxy-3-1] DEBUG io.netty.util.Recycler - -Dio.netty.recycler.batchFastThreadLocalOnly: true
2025-07-16 14:51:22.234 [DefaultDispatcher-worker-1] DEBUG c.zaxxer.hikari.pool.ProxyConnection - HikariPool-1 - Executed rollback on connection com.mysql.cj.jdbc.ConnectionImpl@3af356f due to dirty commit state on close().
2025-07-16 14:51:22.268 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:51:22.272 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:51:34.002 [eventLoopGroupProxy-4-1] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:51:45.437 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:51:45.438 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:52:15.443 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:52:15.444 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:52:45.445 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:52:45.446 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:53:15.452 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:53:15.452 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:53:32.009 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 14:53:32.009 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 14:53:32.011 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 14:53:32.028 [eventLoopGroupProxy-4-3] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:53:32.029 [eventLoopGroupProxy-4-2] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:53:38.182 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 14:53:38.187 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 14:53:41.244 [eventLoopGroupProxy-4-4] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 14:53:45.458 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:53:45.459 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:54:15.464 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:54:15.465 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:54:45.471 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:54:45.471 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:55:15.475 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:55:15.475 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:55:45.478 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:55:45.478 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:56:15.479 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:56:15.480 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:56:45.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:56:45.486 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:57:15.492 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:57:15.492 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:57:45.498 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:57:45.499 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:58:15.505 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:58:15.505 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:58:45.511 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:58:45.512 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:59:15.512 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:59:15.513 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 14:59:45.518 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 14:59:45.519 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:00:15.523 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:00:15.523 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:00:45.524 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:00:45.525 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:01:15.534 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:01:15.534 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:01:45.543 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:01:45.544 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:02:15.554 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:02:15.554 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:02:45.559 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:02:45.560 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:03:15.570 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:03:15.571 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:03:45.582 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:03:45.583 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:04:15.593 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:04:15.593 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:04:45.604 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:04:45.606 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:05:15.609 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:05:15.610 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:05:45.612 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:05:45.612 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:06:15.616 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:06:15.617 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:06:45.620 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:06:45.620 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:07:15.622 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:07:15.623 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:07:45.627 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:07:45.628 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:08:15.634 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:08:15.634 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:08:45.639 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:08:45.640 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:08:50.291 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 15:08:50.294 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 15:08:50.296 [eventLoopGroupProxy-4-5] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 15:09:15.641 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:09:15.642 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:09:45.647 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:09:45.649 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:10:01.320 [DefaultDispatcher-worker-2] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users ORDER BY users.created_at DESC
2025-07-16 15:10:01.320 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT COUNT(*) FROM requirements
2025-07-16 15:10:01.323 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements ORDER BY requirements.created_at DESC LIMIT 20
2025-07-16 15:10:01.324 [eventLoopGroupProxy-4-7] INFO  Application - 200 OK: GET /api/users/all - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 15:10:01.327 [eventLoopGroupProxy-4-6] INFO  Application - 200 OK: GET /api/requirements?page=1&pageSize=20 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 15:10:09.542 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT requirements.id, requirements.title, requirements.business_description, requirements.acceptance_criteria, requirements.status, requirements.priority_importance, requirements.priority_urgency, requirements.estimated_value, requirements.target_users, requirements.business_goal, requirements.creator_id, requirements.assignee_id, requirements.expected_delivery_date, requirements.actual_delivery_date, requirements.created_at, requirements.updated_at FROM requirements WHERE requirements.id = 2
2025-07-16 15:10:09.545 [DefaultDispatcher-worker-1] DEBUG Exposed - SELECT users.id, users.username, users.password, users.nickname, users.avatar_url, users.user_type, users.failed_login_count, users.status, users.locked_until, users.created_at, users.updated_at FROM users WHERE users.id = 2
2025-07-16 15:10:09.547 [eventLoopGroupProxy-4-8] INFO  Application - 400 Bad Request: PUT /api/requirements/2 - Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
2025-07-16 15:10:15.651 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:10:15.652 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:10:45.656 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:10:45.656 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:11:15.580 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:11:15.581 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:11:45.586 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:11:45.587 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:12:15.592 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:12:15.593 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:12:45.600 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:12:45.602 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:13:15.605 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:13:15.605 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:13:45.609 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=20, active=0, idle=20, waiting=0)
2025-07-16 15:13:45.610 [HikariPool-1 housekeeper] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool has sufficient level or currently being filled (queueDepth=0).
2025-07-16 15:13:48.077 [eventLoopGroupProxy-3-3] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-3
2025-07-16 15:13:48.078 [eventLoopGroupProxy-3-2] DEBUG io.netty.buffer.PoolThreadCache - Freed 5 thread-local buffer(s) from thread: eventLoopGroupProxy-3-2
2025-07-16 15:13:48.077 [eventLoopGroupProxy-3-1] DEBUG io.netty.buffer.PoolThreadCache - Freed 5 thread-local buffer(s) from thread: eventLoopGroupProxy-3-1
2025-07-16 15:13:48.078 [eventLoopGroupProxy-3-4] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-4
2025-07-16 15:13:48.078 [eventLoopGroupProxy-3-5] DEBUG io.netty.buffer.PoolThreadCache - Freed 4 thread-local buffer(s) from thread: eventLoopGroupProxy-3-5
2025-07-16 15:13:48.077 [eventLoopGroupProxy-3-6] DEBUG io.netty.buffer.PoolThreadCache - Freed 3 thread-local buffer(s) from thread: eventLoopGroupProxy-3-6
2025-07-16 15:13:49.114 [KtorShutdownHook] INFO  Application - Application stopping: io.ktor.server.application.Application@3c3820bb
2025-07-16 15:13:49.117 [KtorShutdownHook] INFO  Application - Application stopped: io.ktor.server.application.Application@3c3820bb
2025-07-16 15:13:50.478 [main] INFO  Application - Autoreload is disabled because the development mode is off.
